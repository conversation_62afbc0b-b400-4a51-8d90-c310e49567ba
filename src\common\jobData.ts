// No data needed for the trigger job, it just signals the start
export interface TriggerJobData {
    id: string; // Unique ID for the trigger job
}

// Data for each individual vendor inventory job
export interface VendorJobData {
    id: string; // Single vendor ID per job
    api_url: string; // API URL for the vendor
    headers: string; // Headers for the API request (JSON string)
    body: string; // Body for the API request (JSON string)
    api_type: string; // Type of API (e.g., REST, SOAP, etc.)
    is_formdata_request: boolean; // Indicates if the request is a form-data request
    diamond_type?: string; 
}

// Data structure for the result of processing a single vendor (example)
export interface VendorFetchResult {
    vendorId: string;
    success: boolean;
    message?: string;
    timestamp: string;
    // Add other relevant fields like itemCount, etc.
}
