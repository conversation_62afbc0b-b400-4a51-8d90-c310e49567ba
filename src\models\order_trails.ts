import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface OrderTrailsAttributes {
    id: string; // id is an auto-generated UUID
    order_id: string;
    user_id: string;
    buy_request_id: string;
    updated_by_id: string;
    payload: string;
    payment_status: string; // pending, paid, failed, canceled
    order_status: string; // pending, processing, shipped, delivered, canceled
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface OrderTrailsCreationAttributes extends Optional<OrderTrailsAttributes, 'id'> {}

interface OrderTrailsInstance
    extends Model<OrderTrailsAttributes, OrderTrailsCreationAttributes>,
        OrderTrailsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type OrderTrailsStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => OrderTrailsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const order_trails = sequelize.define<OrderTrailsInstance>(
        'order_trails',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            updated_by_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            payment_status: {
                type: DataTypes.ENUM('PENDING', 'PAID', 'FAILED', 'CANCELED'),
                allowNull: false
            },
            order_status: {
                type: DataTypes.ENUM('PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELED'),
                allowNull: false
            },
            payload: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as OrderTrailsStatic;
    //
    // await order_trails.sync({ alter: true })

    return order_trails;
};
