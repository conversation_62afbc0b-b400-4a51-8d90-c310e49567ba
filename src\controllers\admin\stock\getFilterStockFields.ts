import { Op } from 'sequelize';
import { whiteColor, fancyColor, round<PERSON>lia<PERSON>, cut<PERSON>lias, intensity<PERSON>lias } from '../../../utils/constants';

class StockFilters {
    async getFilters(filterObject: any) {
        const filterObjectArray: any[] = [];

        // if (filterObject.treatment) {
        //   filterObjectArray.push({
        //     $or: [
        //       {
        //         treatment: {
        //           $regex: ".*" + "Lab Grown" + ".*",
        //           $options: "i",
        //         },
        //       },
        //       {
        //         treatment: {
        //           $regex: ".*" + "Lab grown" + ".*",
        //           $options: "i",
        //         },
        //       },
        //       {
        //         treatment: {
        //           $regex: ".*" + "lab Grown" + ".*",
        //           $options: "i",
        //         },
        //       },
        //       {
        //         treatment: {
        //           $regex: ".*" + "lab grown" + ".*",
        //           $options: "i",
        //         },
        //       },
        //       {
        //         treatment: {
        //           $regex: ".*" + "labgrown" + ".*",
        //           $options: "i",
        //         },
        //       },
        //       {
        //         treatment: {
        //           $regex: ".*" + "lab_grown" + ".*",
        //           $options: "i",
        //         },
        //       },
        //       {
        //         treatment: {
        //           $regex: ".*" + "lab-grown" + ".*",
        //           $options: "i",
        //         },
        //       },
        //     ],
        //   });
        // }

        if (filterObject.shape) {
            // For shape value
            const shapeArrayObject = this.getShapeValues(filterObject.shape);
            if (shapeArrayObject && shapeArrayObject.totalShapes && shapeArrayObject.totalShapes.length > 0) {
                filterObjectArray.push({
                    shape: {
                        [Op.in]: shapeArrayObject.totalShapes
                    }
                });
            }

            if (
                shapeArrayObject &&
                shapeArrayObject.totalNotIncludeShapes &&
                shapeArrayObject.totalNotIncludeShapes.length > 0
            ) {
                filterObjectArray.push({
                    shape: {
                        [Op.notIn]: shapeArrayObject.totalNotIncludeShapes
                    }
                });
            }
        }

        // For White Colour
        if (filterObject.white_color) {
            const updatedArray: any = [];
            filterObject.white_color.map((colorItem: any) => {
                if (colorItem === 'm+') {
                    updatedArray.push('n');
                    updatedArray.push('o');
                    updatedArray.push('p');
                    updatedArray.push('q');
                    updatedArray.push('r');
                    updatedArray.push('s');
                    updatedArray.push('t');
                    updatedArray.push('u');
                    updatedArray.push('v');
                    updatedArray.push('w');
                    updatedArray.push('x');
                    updatedArray.push('y');
                    updatedArray.push('z');
                } else {
                    updatedArray.push(colorItem);
                }
            });
            const orArray: any = [];
            updatedArray.map((updatedArrayItem) => {
                orArray.push({
                    color: {
                        [Op.iLike]: `%${updatedArrayItem}%`
                    }
                });
            });
            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
            // filterObjectArray.push({
            //   color: {
            //     $in: updatedArray,
            //   },
            // });
        }

        // For Fancy Colour
        if (filterObject.fancy_color) {
            // filterObjectArray.push({
            //   fancy_color: {
            //     $in: filterObject.fancy_color,
            //   },
            // });
            const orArray: any = [];
            filterObject.fancy_color.map((fancyItem) => {
                orArray.push({
                    fancy_color: {
                        [Op.iLike]: `%${fancyItem}%`
                        // $regex: '.*' + fancyItem + '.*',
                        // $options: 'i'
                    }
                });
            });
            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
        }

        if (!filterObject.white_color && !filterObject.fancy_color) {
            // For White Colour
            if (filterObject.color_type) {
                if (filterObject.color_type === 'white') {
                    // filterObjectArray.push({
                    //   color: {
                    //     $in: whiteColor,
                    //   },
                    // });
                    const orArray: any = [];
                    whiteColor.map((colorItem) => {
                        orArray.push({
                            color: {
                                [Op.iLike]: `%${colorItem}%`
                                // $regex: `^${colorItem}$`,
                                // $options: 'i'
                            }
                        });
                    });

                    if (orArray.length) {
                        filterObjectArray.push({
                            [Op.or]: orArray
                        });
                    }
                } else if (filterObject.color_type === 'fancy') {
                    const orArray: any = [];
                    fancyColor.map((fancyItem) => {
                        orArray.push({
                            fancy_color: {
                                [Op.iLike]: `%${fancyItem}%`
                                // $regex: '.*' + fancyItem + '.*',
                                // $options: 'i'
                            }
                        });
                    });
                    if (orArray.length) {
                        filterObjectArray.push({
                            [Op.or]: orArray
                        });
                    }
                }
            }
        }

        // For Fancy Intensity
        if (filterObject.fancy_color_intensity) {
            const orArray: any = [];
            filterObject.fancy_color_intensity.map((fancyColorIntensityItem) => {
                orArray.push({
                    fancy_color_intensity: {
                        [Op.iLike]: `%${fancyColorIntensityItem}%`
                        // $regex: `^${fancyColorIntensityItem}$`,
                        // $options: 'i'
                    }
                });
            });

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
            // filterObjectArray.push({
            //   fancy_color_intensity: {
            //     $in: filterObject.fancy_color_intensity,
            //   },
            // });
        }

        // For Fancy Overtone
        if (filterObject.fancy_color_overtone) {
            const orArray: any = [];
            filterObject.fancy_color_overtone.map((fancyColorOvertoneItem) => {
                orArray.push({
                    fancy_color_overtone: {
                        [Op.iLike]: `%${fancyColorOvertoneItem}%`
                        // $regex: `^${fancyColorOvertoneItem}$`,
                        // $options: 'i'
                    }
                });
            });

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }

            // filterObjectArray.push({
            //   fancy_color_overtone: {
            //     $in: filterObject.fancy_color_overtone,
            //   },
            // });
        }

        // For Clarity
        if (filterObject.clarity) {
            const orArray: any = [];
            filterObject.clarity.map((clarityItem) => {
                orArray.push({
                    clarity: {
                        [Op.iLike]: `%${clarityItem}%`
                        // $regex: `^${clarityItem}$`,
                        // $options: 'i'
                    }
                });
            });

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }

            // filterObjectArray.push({
            //   clarity: {
            //     $in: filterObject.clarity,
            //   },
            // });
        }

        // For Cut
        if (filterObject.cut) {
            // For shape value
            const cutArrayObject: any = this.getCutValues(filterObject.cut);
            const orArray: any = [];

            if (cutArrayObject && cutArrayObject.totalCut && cutArrayObject.totalCut.length > 0) {
                cutArrayObject.totalCut.map((cutItem) => {
                    orArray.push({
                        cut: {
                            [Op.iLike]: `%${cutItem}%`
                            // $regex: `^${cutItem}$`,
                            // $options: 'i'
                        }
                    });
                });
            }

            orArray.push({
                cut: null
            });
            orArray.push({
                cut: ''
            });
            orArray.push({
                cut: '-'
            });

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
        }

        // if (filterObject.cut) {
        //   const orArray = [];
        //   filterObject.cut.map((cutItem) => {
        //     orArray.push({
        //       cut: {
        //         $regex: `^${cutItem}$`,
        //         $options: "i",
        //       },
        //     });
        //   });
        //   orArray.push({
        //     cut: null,
        //   });
        //   orArray.push({
        //     cut: "",
        //   });
        //   orArray.push({
        //     cut: "-",
        //   });
        //   filterObjectArray.push({
        //     $or: orArray,
        //   });
        //   // filterObjectArray.push({
        //   //   cut: {
        //   //     $in: filterObject.cut,
        //   //   },
        //   // });
        // }

        // For Lab
        if (filterObject.lab) {
            const orArray: any = [];
            const norArray: any = [];
            let notInArray: any = [];
            filterObject.lab.map((labItem) => {
                if (String(labItem).toLocaleLowerCase() === 'other') {
                    notInArray = ['none', 'igi', 'gia', 'hrd'];
                } else {
                    notInArray.pop(String(labItem).toLocaleLowerCase());
                    orArray.push({
                        lab: {
                            [Op.iLike]: `%${labItem}%`
                            // $regex: `^${labItem}$`,
                            // $options: 'i'
                        }
                    });
                }
            });

            notInArray.map((labItem: any) => {
                norArray.push({
                    lab: {
                        [Op.iLike]: `%${labItem}%`
                        // $regex: `^${labItem}$`,
                        // $options: 'i'
                    }
                });
            });

            const finalOrArray: any = [];
            if (orArray.length) {
                finalOrArray.push({
                    [Op.or]: orArray
                });
            }

            if (norArray.length) {
                finalOrArray.push({
                    [Op.and]: norArray.map((element: any) => {
                        return { [Op.not]: element };
                    })
                });
            }

            filterObjectArray.push({
                [Op.or]: finalOrArray
            });

            // filterObjectArray.push({
            //   lab: {
            //     $in: filterObject.lab,
            //   },
            // });
        }

        // For Polish
        // if (filterObject.polish) {
        //   const orArray = [];
        //   filterObject.polish.map((polishItem) => {
        //     orArray.push({
        //       polish: {
        //         $regex: `^${polishItem}$`,
        //         $options: "i",
        //       },
        //     });
        //   });
        //   filterObjectArray.push({
        //     $or: orArray,
        //   });
        //   // filterObjectArray.push({
        //   //   polish: {
        //   //     $in: filterObject.polish,
        //   //   },
        //   // });
        // }
        if (filterObject.polish) {
            // For shape value
            const polishArrayObject = this.getCutValues(filterObject.polish);
            const orArray: any = [];

            if (polishArrayObject && polishArrayObject.totalCut && polishArrayObject.totalCut.length > 0) {
                polishArrayObject.totalCut.map((polishItem) => {
                    orArray.push({
                        polish: {
                            [Op.iLike]: `%${polishItem}%`
                            // $regex: `^${polishItem}$`,
                            // $options: 'i'
                        }
                    });
                });
            }

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
        }

        if (filterObject.growth_type) {
            const orArray: any = [];
            const norArray: any = [];

            if (filterObject.growth_type.includes('other')) {
                if (!filterObject.growth_type.includes('cvd')) {
                    norArray.push({
                        growth_type: {
                            [Op.iLike]: `%cvd%`
                            // $regex: `^cvd$`,
                            // $options: 'i'
                        }
                    });
                    norArray.push({
                        growth_type: {
                            [Op.iLike]: `%type iia%`
                            // $regex: `^type iia$`,
                            // $options: 'i'
                        }
                    });
                }

                if (!filterObject.growth_type.includes('hpht')) {
                    norArray.push({
                        growth_type: {
                            [Op.iLike]: `%hpht%`
                            // $regex: `^hpht$`,
                            // $options: 'i'
                        }
                    });
                    norArray.push({
                        growth_type: {
                            [Op.iLike]: `%type ii%`
                            // $regex: `^type ii$`,
                            // $options: 'i'
                        }
                    });
                }
            } else if (filterObject.growth_type.includes('cvd')) {
                orArray.push({
                    growth_type: {
                        [Op.iLike]: `%cvd%`
                        // $regex: `^cvd$`,
                        // $options: 'i'
                    }
                });
                orArray.push({
                    growth_type: {
                        [Op.iLike]: `%type iia%`
                        // $regex: `^type iia$`,
                        // $options: 'i'
                    }
                });
            } else if (filterObject.growth_type.includes('hpht')) {
                orArray.push({
                    growth_type: {
                        [Op.iLike]: `%hpht%`
                        // $regex: `^hpht$`,
                        // $options: 'i'
                    }
                });
                orArray.push({
                    growth_type: {
                        [Op.iLike]: `%type ii%`
                        // $regex: `^type ii$`,
                        // $options: 'i'
                    }
                });
            }

            const finalOrArray: any = [];
            if (orArray.length) {
                finalOrArray.push({
                    [Op.or]: orArray
                });
            }

            if (norArray.length) {
                finalOrArray.push({
                    [Op.and]: norArray.map((element: any) => {
                        return { [Op.not]: element };
                    })
                });
            }

            if (finalOrArray.length) {
                filterObjectArray.push({
                    [Op.or]: finalOrArray
                });
            }
        }

        if (filterObject.certificationId) {
            // For certificationId
            filterObjectArray.push({
                [Op.or]: [
                    {
                        certificate_number: {
                            [Op.iLike]: `%${filterObject.certificationId}%`
                            // $regex: '.*' + filterObject.certificationId + '.*',
                            // $options: 'i'
                        }
                    },
                    {
                        stock_id: {
                            [Op.iLike]: `%${filterObject.certificationId}%`
                            // $regex: '.*' + filterObject.certificationId + '.*',
                            // $options: 'i'
                        }
                    }
                ]
            });
        }

        // For Symmetry
        // if (filterObject.symmetry) {
        //   const orArray = [];
        //   filterObject.symmetry.map((symmetryItem) => {
        //     orArray.push({
        //       symmetry: {
        //         $regex: `^${symmetryItem}$`,
        //         $options: "i",
        //       },
        //     });
        //   });
        //   filterObjectArray.push({
        //     $or: orArray,
        //   });
        //   // filterObjectArray.push({
        //   //   symmetry: {
        //   //     $in: filterObject.symmetry,
        //   //   },
        //   // });
        // }
        if (filterObject.symmetry) {
            // For shape value
            const symmetryObject = this.getCutValues(filterObject.symmetry);
            const orArray: any = [];

            if (symmetryObject && symmetryObject.totalCut && symmetryObject.totalCut.length > 0) {
                symmetryObject.totalCut.map((symmetryItem) => {
                    orArray.push({
                        polish: {
                            [Op.iLike]: `%${symmetryItem}%`
                            // $regex: `^${symmetryItem}$`,
                            // $options: 'i'
                        }
                    });
                });
            }

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
        }

        // For fluorescence_intensity fluorescence_intensity
        if (filterObject.fluorescence_intensity) {
            // For intensity value
            const intensityObject = this.getIntensityValues(filterObject.fluorescence_intensity);

            if (
                intensityObject &&
                intensityObject.totalFlurosenceItemArray &&
                intensityObject.totalFlurosenceItemArray.length > 0
            ) {
                const orArray: any = [];
                intensityObject.totalFlurosenceItemArray.map((fluorescenceIntensityItem) => {
                    orArray.push({
                        fluorescence_intensity: {
                            [Op.iLike]: `%${fluorescenceIntensityItem}%`
                            // $regex: `^${fluorescenceIntensityItem}$`,
                            // $options: 'i'
                        }
                    });
                });

                if (orArray.length) {
                    filterObjectArray.push({
                        [Op.or]: orArray
                    });
                }
            }

            // filterObjectArray.push({
            //   fluorescence_intensity: {
            //     $in: filterObject.fluorescence_intensity,
            //   },
            // });
        }

        if (filterObject.diamond_type === 'lab_grown') {
            filterObjectArray.push({
                is_lab_grown: true
            });
        } else if (filterObject.diamond_type === 'natural') {
            filterObjectArray.push({
                is_lab_grown: false
            });
        }

        // For status(AVAILABLE, HOLD, SOLD)
        if (filterObject.status) {
            filterObjectArray.push({
                status: {
                    [Op.in]: filterObject.status.map((item) => String(item).toLocaleUpperCase())
                }
            });
        }

        // WEIGHT RANGE (size)
        if (filterObject.size) {
            const orSizeArray: any = [];
            filterObject.size.forEach((item) => {
                const sizeArray = item.split('-');
                const startSize = parseFloat(sizeArray[0]);
                const endSize = parseFloat(sizeArray[sizeArray.length - 1]);

                orSizeArray.push({
                    [Op.and]: [
                        {
                            weight: {
                                [Op.gte]: startSize
                            }
                        },
                        {
                            weight: {
                                [Op.lte]: endSize
                            }
                        }
                    ]
                });
            });

            if (orSizeArray.length) {
                filterObjectArray.push({
                    [Op.or]: orSizeArray
                });
            }
        }

        // For Admins
        if (filterObject.adminIds && filterObject.adminIds.length) {
            filterObjectArray.push({
                admin_id: {
                    [Op.in]: filterObject.adminIds.map((item) => String(item))
                }
            });
        }

        // For Country
        if (filterObject.country) {
            const orArray: any = [];

            filterObject.country.map((countryItem) => {
                orArray.push({
                    country: {
                        [Op.iLike]: `%${countryItem}%`
                        // $regex: `^${countryItem}$`,
                        // $options: 'i'
                    }
                });
            });

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
        }

        // For Treatment
        const treatmentTypes = ['hpht'];
        if (filterObject.treatment) {
            const orArray: any = [];
            filterObject.treatment.map((treatmentItem) => {
                orArray.push({
                    treatment: {
                        [Op.iLike]: `%${treatmentItem}%`
                        // $regex: '.*' + treatmentItem + '.*',
                        // $options: 'i'
                    }
                });
            });

            if (orArray.length) {
                filterObjectArray.push({
                    [Op.or]: orArray
                });
            }
        } else {
            const norArray: any = [];
            treatmentTypes.map((treatmentItem) => {
                norArray.push({
                    treatment: {
                        [Op.iLike]: `%${treatmentItem}%`
                        // $regex: '.*' + treatmentItem + '.*',
                        // $options: 'i'
                    }
                });
            });

            if (norArray.length) {
                filterObjectArray.push({
                    [Op.and]: norArray.map((element: any) => {
                        return { [Op.not]: element };
                    })
                });
            }
        }

        return filterObjectArray;
    }

    getShapeValues(shapeArray: any) {
        // For In query
        let totalShapes = [];
        // For not In query
        let totalNotIncludeShapes = [];

        for (const shapeItem of shapeArray) {
            // get alias value for shapeItem
            if (String(shapeItem).toLocaleLowerCase() === 'other') {
                // check if shap string is other
                // loop on all alias
                for (const eachItem of Object.keys(roundAlias)) {
                    const pushArrayData = roundAlias[String(eachItem).toLocaleLowerCase()];
                    if (pushArrayData) {
                        totalNotIncludeShapes = totalNotIncludeShapes.concat(pushArrayData);
                    }
                }
            }

            // shapItem is not having a value other
            const pushArray = roundAlias[String(shapeItem).toLocaleLowerCase()];
            if (pushArray) {
                totalShapes = totalShapes.concat(pushArray);
            }
        }

        return {
            totalShapes: totalShapes.map((data: any) => data.toLocaleLowerCase()),
            totalNotIncludeShapes: totalNotIncludeShapes.map((data: any) => data.toLocaleLowerCase())
        };
    }

    getCutValues(cutArray: any) {
        // For In query
        let totalCut = [];

        for (const cutItem of cutArray) {
            // shapItem is not having a value other
            const pushArray = cutAlias[String(cutItem).toLocaleLowerCase()];
            if (pushArray) {
                totalCut = totalCut.concat(pushArray);
            }
        }

        return {
            totalCut: totalCut.map((data: any) => data.toLocaleLowerCase())
        };
    }

    getIntensityValues(intensityItemArray: any) {
        // For In query
        let totalFlurosenceItemArray = [];

        for (const intensityItem of intensityItemArray) {
            // shapItem is not having a value other
            const pushArray = intensityAlias[String(intensityItem).toLocaleLowerCase()];

            if (pushArray) {
                totalFlurosenceItemArray = totalFlurosenceItemArray.concat(pushArray);
            }
        }

        return {
            totalFlurosenceItemArray: totalFlurosenceItemArray.map((data: any) => data.toLocaleLowerCase())
        };
    }
}

export default new StockFilters();
