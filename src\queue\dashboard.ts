import express from 'express';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bullmq';
import dotenv from 'dotenv';

// Load environment variables (same as your workers/creator)
dotenv.config();

// Import Redis connection options and queue names
import { redisConnection } from './../common/redisConnection';
import { TRIGGER_QUEUE_NAME, VENDOR_QUEUE_NAME } from './../common/queueNames';
import { VendorJobData } from './../common/jobData'; // Import if needed for Queue typing
import { logger } from './../utils/logger';

// --- Queue Instances for the Dashboard ---
// We need Queue instances here *only* to connect them to Bull Board.
// These instances do NOT process jobs. They just provide the connection.

const triggerQueue = new Queue(TRIGGER_QUEUE_NAME, {
    connection: redisConnection
});

const vendorQueue = new Queue<VendorJobData>(VENDOR_QUEUE_NAME, {
    // Use job data type if defined
    connection: redisConnection
});

// --- Bull Board Setup ---

// 1. Create the Express adapter
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/ui'); // Base path for the dashboard UI

// 2. Create the Bull Board instance and add queues
createBullBoard({
    queues: [
        new BullMQAdapter(triggerQueue), // Add the trigger queue
        new BullMQAdapter(vendorQueue) // Add the vendor processing queue
    ],
    serverAdapter // Attach the Express adapter
});

// --- Express Server ---
const app = express();

// Mount the Bull Board UI router
app.use('/ui', serverAdapter.getRouter());

// Optional: Add a simple root route for health check or info
app.get('/', (req, res) => {
    res.send(`
        <h1>BullMQ Dashboard</h1>
        <p>Visit <a href="/ui">/ui</a> to view the Bull Board dashboard.</p>
        <p>Monitoring queues: ${TRIGGER_QUEUE_NAME}, ${VENDOR_QUEUE_NAME}</p>
    `);
});

// --- Start Server ---
const PORT = process.env.BULL_DASHBOARD_PORT || 9999; // Use an environment variable for port
app.listen(PORT, () => {
    logger.info(`Bull Board dashboard server running on http://localhost:${PORT}/ui`);
    logger.info(`Monitoring queues: ${TRIGGER_QUEUE_NAME}, ${VENDOR_QUEUE_NAME}`);
});

// --- Graceful Shutdown (Optional but Recommended) ---
const gracefulShutdown = async () => {
    logger.info('Shutting down dashboard server...');
    // You might close Redis connections if they were explicitly opened here,
    // but BullMQ generally manages its connections within the Queue instances.
    // Closing the server is usually sufficient.
    process.exit(0);
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);
