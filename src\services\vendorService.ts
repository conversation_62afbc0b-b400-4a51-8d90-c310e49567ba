import axios from 'axios';
import { VendorFetchResult, VendorJobData } from './../common/jobData';
import { saveCsvFile } from './../controllers/ProductCron';
import FormData from 'form-data';
import models from './../models';
import { Op } from 'sequelize';
import { logger } from './../utils/logger';

export const getAllVendorIds = async (): Promise<VendorJobData[]> => {
    logger.info('Fetching all vendor IDs from database...');
    const data = await models.vendors.findAll({
        where: {
            [Op.and]: [
                { vendor_type: 'API' },
                { is_blacklisted: false },
                { is_terms_accepted: true },
                { is_verified: true },
                { _deleted: false },
                { is_active: true }
            ]
        },
        attributes: [
            'id',
            'api_url',
            'headers',
            'body',
            'api_type',
            'is_formdata_request',
            'api_url_natural',
            'headers_natural',
            'body_natural',
            'api_type_natural',
            'is_formdata_request_natural',
            'is_natural_api_same'
        ],
        raw: true
    });

    if (data?.length) {
        logger.info(`Finding vendor IDs with different natural api setup.`);

        const naturalApiVendors: any = data?.filter((vendor: any) => !vendor.is_natural_api_same);

        logger.info(`Found ${naturalApiVendors.length} vendor IDs with different natural api setup.`);

        /// rename attributes
        const naturalVendorData: any = naturalApiVendors?.map((vendor: any) => {
            return {
                id: vendor?.id,
                api_url: vendor?.api_url_natural,
                headers: vendor?.headers_natural,
                body: vendor?.body_natural,
                api_type: vendor?.api_type_natural,
                is_formdata_request: vendor?.is_formdata_request_natural,
                diamond_type: 'natural'
            };
        });

        data.push(...naturalVendorData);
    }

    logger.info(`Found ${data.length} vendor IDs.`);
    return data;
};

// Simulates fetching inventory for a single vendor via their API
export const fetchVendorInventory = async (vendor: VendorJobData): Promise<VendorFetchResult> => {
    logger.info(`[PID: ${process.pid}] Fetching inventory for vendor: ${vendor.id}...`);
    // *** Replace with your actual API call logic for the vendor ***
    // Example: Make an HTTP request to the vendor's API endpoint
    try {
        logger.info(`!!!!!!!!!!Started For Vendor!!!!!!!!! ${vendor.id}`);

        const url = vendor.api_url;
        logger.info(`!!!URL!!! ${url}`);

        if (!url) {
            const fetchResult: VendorFetchResult = {
                vendorId: vendor.id,
                success: false,
                message: 'API URL is missing.',
                timestamp: new Date().toISOString()
            };

            return fetchResult;
        }
        const config = {
            headers: vendor.headers ? JSON.parse(vendor.headers) : {}
        };
        const data = typeof vendor.body === 'string' ? JSON.parse(vendor.body) : vendor.body;

        logger.info(`!!!config!!! ${JSON.stringify(config, null, 2)}`);
        logger.info(`!!!data!!! ${JSON.stringify(data, null, 2)}`);
        logger.info(`!!!Type!!! ${vendor.api_type}`);

        let apiRes;

        if (vendor.is_formdata_request) {
            logger.info(`!!!!!!!!!FormData Call!!!!!!!!!1`);
            const formData = new FormData();
            for (const bodyItem of Object.keys(data)) {
                formData.append(bodyItem, data[bodyItem]);
            }

            const response = await axios.request({
                url,
                method: vendor.api_type || 'post',
                data: formData,
                headers: config.headers
            });

            apiRes = response?.data;
        } else {
            switch (String(vendor.api_type).toLowerCase()) {
                case 'get':
                    apiRes = await axios.get(url, config);
                    break;
                case 'post':
                    apiRes = await axios.post(url, data, config);
                    break;
                default:
                    apiRes = await axios.get(url, config);
                    break;
            }
            apiRes = apiRes?.data;
        }

        if (apiRes) {
            let stockData;
            if (apiRes?.data) {
                stockData = apiRes?.data;
            } else if (apiRes?.details) {
                stockData = apiRes?.details;
            } else if (apiRes?.GetStockResult?.Data) {
                stockData = apiRes?.GetStockResult?.Data;
            } else if (apiRes?.StoneList) {
                stockData = apiRes?.StoneList;
            } else if (apiRes?.Data) {
                stockData = apiRes?.Data;
            } else if (apiRes?.DataList) {
                stockData = apiRes?.DataList;
            } else if (apiRes?.UserData) {
                stockData = apiRes?.UserData;
            } else {
                stockData = apiRes;
            }

            const typeOfStockData = typeof stockData;
            logger.info(`!!!!!typeOfStockData!!!!!!!! ${typeOfStockData}`);
            if (typeOfStockData === 'string') {
                stockData = JSON.parse(stockData);
                if (stockData?.ViPacketListForAPIResult) {
                    stockData = stockData?.ViPacketListForAPIResult;
                }
            }

            const cronSuccessful = true;
            const cronErr: string[] = [];
            logger.info(`!!!!!!!stockData Length!!!!!!!!! ${stockData?.length}`);

            if (stockData?.length) {
                try {
                    const savedCsvPath = await saveCsvFile(stockData, `converted_file${Date.now()}.csv`);
                    logger.info(`!!!!!!!savedCsvPath!!!!!!!!!!! ${savedCsvPath}`);

                    const addStockPayload = {
                        vendor_id: vendor.id,
                        filePath: savedCsvPath,
                        diamond_type: vendor?.diamond_type
                    };

                    const addCsvStockConfig = {
                        headers: {
                            authorization: process.env.ADD_STOCK_AUTH
                        }
                    };

                    await axios.post(process.env.ADD_STOCK_URL ?? '', addStockPayload, addCsvStockConfig);

                    try {
                        await models.cron_logs.create({
                            vendor_id: vendor.id,
                            is_successful: cronSuccessful,
                            data: `!!!!!!!!!Stock Data Length!!!!!!!! ${stockData?.length}`,
                            message: cronSuccessful ? JSON.stringify(apiRes.data) : JSON.stringify(cronErr),
                            type: 'cron'
                        });
                    } catch (err) {
                        logger.error('error in user cron chunk creation logs', err);
                        throw err;
                    }
                } catch (error) {
                    logger.error('error in user cron chunk creation ', error);
                    try {
                        await models.cron_logs.create({
                            vendor_id: vendor.id,
                            is_successful: false,
                            data: `!!!!!!!!!Stock Data Length!!!!!!!! ${stockData?.length}`,
                            message: JSON.stringify(error),
                            type: 'cron'
                        });
                    } catch (err) {
                        logger.error('error in user cron chunk creation logs', err);
                    }
                }
            }
        }
        logger.info(`!!!!!!!!!!Completed For Vendor!!!!!!!!! ${vendor.id}`);
    } catch (error) {
        logger.error('getStockData error==>', error);
        try {
            models.cron_logs.create({
                vendor_id: vendor.id,
                is_successful: false,
                data: `!!!!!!!!!Stock Data Length!!!!!!!! 0`,
                message: JSON.stringify(error),
                type: 'cron'
            });
        } catch (err) {
            logger.error('error in user cron chunk creation logs', err);
        }

        throw error;
    }

    const result: VendorFetchResult = {
        vendorId: vendor.id,
        success: true,
        message: 'Inventory fetched and processed successfully.',
        timestamp: new Date().toISOString()
    };

    return result;
};
