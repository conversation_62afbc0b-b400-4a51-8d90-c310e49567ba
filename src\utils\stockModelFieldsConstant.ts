export const getStockFieldsAliases = {
    stockId:
        'Lot Name, lot name, ReferenceNum, ReferenceNumber, Stock, Stock Num, Stock_no, StockNo, StockNum, StockNumber, VendorStockNumber, VenderStockNumber, StockId, stockid, stock_id',
    discount:
        'disc(%), Disc(%), PctRapNetDiscount, Rap netDisc, RapnetDiscount, rapnetdiscount, RapnetDiscountPct, RapnetDiscountPercent, RapnetDiscPct, RapnetDpx, RapnetRapPct, RDisc, RDiscount, RDiscountPct, RDiscountPercent, RDiscPct, RDpx, RRapPct, RapNet Discount Price, discounts',
    pricePerCaret:
        '$/ct, $/Ct, RapnetAskingPrice, AskingPrice, PerCarat, PerCt, Prc, Price, PriceCarat, PriceCt, PricePerCarat, pricepercarat, PricePerCt, Px, price_per_caret',
    weight: 'Carat, <PERSON>tSize, CaratWeight, Ct, CtSize, CtWeight, Weight, Sz, Weight, weight, carat',
    rapnet_lot_id: 'rapnetlotid, RapNetLotID',
    availability: 'Availability, Avail, Availability, Available, Status, availability, status',
    shape: 'SHAPE, Shape, Shp, shape',
    color: 'Color, Colr, Colour, colour, color',
    clarity: 'Clar, Clarity, Clearity, Purity, clarity',
    cut: 'Cut, CutGrade, cut',
    polish: 'Finish, Pol, Pol., Polish, polish, pol.',
    symmetry: 'Symmetry, Sym, Sym., Symetry, Sym-metry, symmetry, sym.',
    fluorescence_intensity:
        'fluor.intesity, Fluor.Intesity, FluorescenceIntensity, fluorescenceintensity, Flr, FlrIntensity, Fluo Intensity, Fluor, Fluor Intensity, Fluorescence, Fluorescence Intensity, FluorescenceIntensity, FluorIntensity, fluorescence_intensity',
    fluorescence_color:
        'fluor.color, Fluor.Color, FlrColor, fluorescencecolor, Fluo Color, Fluor Color, FluorColor, FluorescenceColor, fluorescence_color',
    measurements: 'Measurement, Measurements, measurements, Dimensions, Meas, Measurement, Measurements, measurement',
    shade: 'Shade, shade',
    milky: 'Milky, milky, Cloudy',
    eye_clean: 'Eye Clean, EyeClean, eyeclean, eye_clean',
    lab: 'Lab, LAB, Cert, Certificate, Laboratory, lab, lab - cert, LAB',
    certificate_number:
        'certificate no, Certificate No, Cert Num, CertID, CertificateID, CertificateNum, CertificateNumber, CertNo, CertNum, CmPub, certificatenumber, certificate_number',
    location: '',
    treatment: '',
    discounts: '',
    price_per_caret:
        '$/ct, $/Ct, RapnetAskingPrice, AskingPrice, PerCarat, PerCt, Prc, Price, PriceCarat, PriceCt, PricePerCarat, pricepercarat, PricePerCt, Px, price_per_caret',
    final_price: '',
    final_price_ori: '',
    meas_depth:
        'Measurements Depth, Measurements Depth, Height, MeasurementsDepth, MeasHeight, measurementsdepth, measurements_depth',
    table: 'Table, Table Percent, TablePct, TablePercent, Tbl, table',
    girdle_thin: 'Girdle, GirdleMin, GirdleThin, girdlethin, girdle_thin',
    girdle_thick: 'GirdleMax, GirdleThick, girdlethick, girdle_thick',
    girdle_per: 'Girdle Percent, GirdlePct, GirdleThicknessInPct, GirdlePercent, girdlepercent, girdle_per',
    girdle_condition: 'Girdle Condition, GirdleCondition, girdlecondition, girdle, girdle_condition',
    culet_size: 'Culet, CuletSize, Culet_size, CuletGrade,Cullet, CulletGrade, culetsize, culet_size',
    culet_condition: 'CuletCondition, Culet Condition, culetcondition, cul, culet_condition',
    crown_height: 'Crown, Crown Percent, CrownHeight, Crown Height, CrownPct, crownheight, crown height, crown_height',
    crown_angle: 'CrownAngle, Crown Angle, crownangle, crown angle, crown_angle',
    pavilion_depth:
        'Pavil, Pavilion, Pavilion Percent, PavilionDepth, Pavilion Depth, PavilionPct, paviliondepth, pavillion depth, pavilion_depth',
    pavilion_angle: 'PavilionAngle, Pavilion Angle, pavilionangle, pavillion angle, pavilion_angle',
    inscription: 'LaserInscription, Laser Inscription, laserinscription, laser_inscription',
    certificate_comment:
        'Comment, Comments, comments, Remarks, Lab comment, Cert comment, Certificate comment, Laboratory comment, Comment, certificate_comment',
    certfile:
        'CertFile, Lab - Cert, Cert File, certfile, CertFilename, CertificateFile, CertificateFilename, CertificateImage, CertImage, File, certificate_image',
    white_inclusion: 'White Inclusions, White Inclusion, WhiteInclusions, whiteinclusions',
    black_inclusion: 'Black Inclusion, BlackInclusion, blackinclusion, black_inclusion',
    fancy_color: 'FancyColor, Fancy Color, FancyColorMainBody, FC-Main Body, FCMainBody, fancycolor, fancy_color',
    fancy_color_intensity: 'FancyColorIntensity, FCIntensity, FC-Intensity, fancycolorintensity, fancy_color_intensity',
    fancy_color_overtone:
        'FancyColorOvertone, FancyColorOvertones, FCOvertone, fancycolorovertone, fancy_color_overtone',
    diamond_video: 'Video Link, VideoLink, videolink, videolink, diamond_video',
    diamond_image: 'DiamondImage, Image, ImageFile, Photo, diamondimage, image, Image, diamond_image',
    diamond_type: 'DiamondType, diamond type, diamondtype, diamond_type',
    lab_grown_type:
        'Enhance, Enhancement, Enhancements, Enhance-ments, Treatments, LabGrownType, labgrowntype, diamond type',
    keytosymbols: 'KeyToSymbols, keytosymbols, Key, key_to_symbol',
    country: 'Country, CountryLocation, LotCountry, country',
    state: 'StateLocation, State, state',
    city: 'City, CityLocation, city',
    diamond_image_2: 'DiamondImage2, diamondimage2',
    hearts_image: 'HeartsImage, heartsimage',
    arrows_image: 'ArrowImage, arrowimage',
    aset_image: 'AsetImage, asetimage',
    idealscope_image: 'IdealscopeImage, idealscopeimage',
    growth_type: 'growth type, growth_type',
    rap_price: 'rap price, Rap Price, rap_price',
    lw_ratio: 'l/w ratio, L/W Ratio',
    brand: 'Brand, brand, origin, Origin',
    status: 'Status, status',
    is_lab_grown: 'DiamondType, diamondtype, diamond_type',
    open_inclusion: 'Open Inclusion, OpenInclusion, openinclusion',
    rap_per_caret: 'rap price, Rap Price, rap_per_caret'
};
