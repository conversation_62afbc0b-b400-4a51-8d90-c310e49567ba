import { ConnectionOptions } from 'bullmq';
import dotenv from 'dotenv';
import { logger } from '../utils/logger';

dotenv.config(); // Load environment variables from .env file

export const redisConnection: ConnectionOptions = {
    host: '*************',
    port: 6379,
    password: 'va1EmxsNF0iQtL8hy+LOcqn2/iSaa34JqJauKcUU/foKeAdaahzxJQ1qG3W+QZDAlUZqNWs7+03Fk/eQ192'
    // Add other Redis options if needed (e.g., db, tls)
};

logger.info(`Connecting to Redis at ${redisConnection.host}:${redisConnection.port}`);
