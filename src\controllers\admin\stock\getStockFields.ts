// return type : String
const getStringValue = (dataObject, fields, shouldNotLowerCase = false) => {
    const fieldsArray = fields.split(', ');
    let returnValue = '';

    for (const item of fieldsArray) {
        if (dataObject[item] && dataObject[item] !== '') {
            returnValue = String(dataObject[item]);
            break;
        }
    }

    if (shouldNotLowerCase) {
        return returnValue;
    }

    return returnValue.toLocaleLowerCase();
};

// return type : Double
const getDoubleValue = (dataObject, fields) => {
    const fieldsArray = fields.split(', ');
    let returnValue = 0.0;

    for (const item of fieldsArray) {
        const regex = /[+-]?\d+(\.\d+)?/g;
        if (dataObject[item] && dataObject[item] !== '') {
            let value = [];
            if (typeof dataObject[item] !== 'string') {
                dataObject[item] = String(dataObject[item]);
            }
            value =
                dataObject[item].match(regex) &&
                dataObject[item].match(regex).map((v) => {
                    return parseFloat(v);
                });

            if (value && value.length > 0 && value[0] !== '' && parseFloat(value[0]) && !isNaN(parseFloat(value[0]))) {
                returnValue = parseFloat(value[0]);
                break;
            }
        }
    }

    return returnValue;
};

// return type : Object
const getObjectValue = (dataObject, fields) => {
    const fieldsArray = fields.split(', ');
    let returnValue: any = {};

    for (const item of fieldsArray) {
        if (dataObject[item]) {
            returnValue = dataObject[item];
            break;
        }
    }

    return returnValue;
};

const getFinalPrice = (weight, pricePerCaret) => {
    return weight * pricePerCaret;
};

const getRapPerCaretPrice = (weight, pricePerCaret) => {
    return weight * pricePerCaret;
};

const getFieldKeys = (keysArray, fieldName) => {
    const index = keysArray.findIndex((data, i) => {
        return data === fieldName;
    });

    return index;
};

export = {
    getStringValue,
    getDoubleValue,
    getFinalPrice,
    getRapPerCaretPrice,
    getFieldKeys,
    getObjectValue
};
