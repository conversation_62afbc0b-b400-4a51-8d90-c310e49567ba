import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface CronLogsAttributes {
    id: string; // id is an auto-generated UUID
    vendor_id: string;
    admin_id: string;
    updatedAt?: Date;
    createdAt?: Date;
    is_successful: boolean;
    data: string;
    message: string;
    type: string;
}

interface CronLogsCreationAttributes extends Optional<CronLogsAttributes, 'id'> {}

interface CronLogsInstance extends Model<CronLogsAttributes, CronLogsCreationAttributes>, CronLogsAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type CronLogsStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => CronLogsInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const cronLogs = sequelize.define<CronLogsInstance>(
        'cron_logs',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            admin_id: {
                type: DataTypes.UUID,
                allowNull: true
            },
            is_successful: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            data: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            message: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            type: {
                type: DataTypes.STRING,
                allowNull: true
            }
        },
        {
            freezeTableName: true
        }
    ) as CronLogsStatic;

    cronLogs.associate = (models) => {
        cronLogs.belongsTo(models.vendors, {
            foreignKey: 'vendor_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        cronLogs.belongsTo(models.admins, {
            foreignKey: 'admin_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    //
    // await cronLogs.sync({ alter: true });

    return cronLogs;
};
