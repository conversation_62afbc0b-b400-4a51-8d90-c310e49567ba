import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface BuyRequestAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    stock_ids: object[];
    vendor_ids: string[];
    status: string;
    updated_by_id: string;
    payment_mode: string;
    payment_type: string;
    shipping_address: object;
    billing_address: object;
    reject_reason: string;
    shipment_id: string;
    shipment_price: number;
    amount: number;
    grand_total: number;
    order_code: string;
    ref_trans_id: string;
    trans_id: string;
    authorize_transaction_payload: string;
    capture_void_transaction_payload: string;
    card_holder_name: string;
    card_number: string;
    card_type: string;
    exp_date: string;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface BuyRequestCreationAttributes extends Optional<BuyRequestAttributes, 'id'> {}

interface BuyRequestInstance extends Model<BuyRequestAttributes, BuyRequestCreationAttributes>, BuyRequestAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type BuyRequestStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => BuyRequestInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const buy_requests = sequelize.define<BuyRequestInstance>(
        'buy_requests',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            stock_ids: {
                type: DataTypes.ARRAY(DataTypes.JSONB),
                allowNull: false
            },
            vendor_ids: {
                type: DataTypes.ARRAY(DataTypes.UUID),
                allowNull: true
            },
            status: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: ['PENDING', 'ACCEPTED', 'UPDATED', 'CANCELED', 'AUTO-CANCELED']
            },
            updated_by_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            payment_mode: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: ['CREDIT_LIMIT', 'CREDIT_CARD', 'APPLE_PAY']
            },
            payment_type: {
                type: DataTypes.ENUM,
                allowNull: false,
                values: ['CREDIT', 'CASH']
            },
            shipping_address: {
                type: DataTypes.JSONB,
                allowNull: false
            },
            billing_address: {
                type: DataTypes.JSONB,
                allowNull: false
            },
            reject_reason: {
                type: DataTypes.STRING,
                allowNull: true
            },
            shipment_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            shipment_price: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            amount: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            grand_total: {
                type: DataTypes.DOUBLE,
                allowNull: false,
                defaultValue: 0
            },
            ref_trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            trans_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            authorize_transaction_payload: {
                type: DataTypes.STRING,
                allowNull: true
            },
            capture_void_transaction_payload: {
                type: DataTypes.STRING,
                allowNull: true
            },
            card_holder_name: {
                type: DataTypes.STRING,
                allowNull: true
            },
            card_number: {
                type: DataTypes.STRING,
                allowNull: true
            },
            card_type: {
                type: DataTypes.STRING,
                allowNull: true
            },
            exp_date: {
                type: DataTypes.STRING,
                allowNull: true
            },
            order_code: {
                type: DataTypes.STRING,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as BuyRequestStatic;

    buy_requests.associate = (models) => {
        buy_requests.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        buy_requests.hasOne(models.orders, {
            foreignKey: 'buy_request_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    //
    // await buy_requests.sync({ alter: true })

    return buy_requests;
};
