{"apps": [{"name": "diamond-company-cron", "script": "./dist/index.js", "merge_logs": true, "log_file": ".logs/combined.outerr.log", "out_file": ".logs/out.log", "error_file": ".logs/err.log", "log_date_format": "YYYY-MM-DD HH:mm Z"}, {"name": "inventory-scheduler", "script": "./dist/queue/scheduler.js", "watch": false, "env": {"NODE_ENV": "production"}}, {"name": "inventory-job-creator", "script": "./dist/queue/jobCreator.js", "watch": false, "env": {"NODE_ENV": "production"}}, {"name": "inventory-worker", "script": "./dist/queue/worker.js", "instances": 2, "exec_mode": "cluster", "watch": false, "max_memory_restart": "500M", "env": {"NODE_ENV": "production"}}, {"name": "bull-dashboard", "script": "./dist/queue/dashboard.js", "watch": false, "env": {"NODE_ENV": "production"}}]}