import models from '../models';
import axios from 'axios';
import { getStockModalFields } from './admin/stock/getStockModelFields';
import { Op } from 'sequelize';
import { logger } from '../utils/logger';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';
import path from 'path';
import { parse } from 'json2csv';
import fs from 'fs';
import FormData from 'form-data';
dotenv.config({ path: path.join(__dirname, '../../.env') });

// for vendor
export const productsCron = async () => {
    const data = await models.vendors.findAll({
        where: {
            [Op.and]: [
                { vendor_type: 'API' },
                { is_blacklisted: false },
                { is_terms_accepted: true },
                { is_verified: true },
                { _deleted: false },
                { is_active: true }
            ]
        }
    });

    logger.info(`!!!!!!!Total Vendors Length!!!!!!!!!! ${data?.length}`);

    const chunkSize = 10;
    const chunkedArray: object[] = [];
    for (let i = 0; i < data.length; i += chunkSize) {
        chunkedArray.push(data.slice(i, i + chunkSize));
    }

    getStockData(chunkedArray);
};

export const AdminProductsCron = async () => {
    const adminObj = await models.admins.findOne({
        where: { email: process.env.ADMIN_EMAIL }
    });

    if (adminObj) {
        const match = await bcrypt.compare(process.env.ADMIN_PASS, adminObj.password);
        if (match) {
            const stocksData = await models.stocks.findAll({
                where: {
                    _deleted: false,
                    admin_id: adminObj.id
                },
                attributes: ['stock_id', 'status', 'admin_id', 'vendor_id', 'certificate_number']
            });
            const stockIds = stocksData?.map((stockItem: any) => stockItem.stock_id);
            const certificateNumbers = stocksData?.map((stockItem: any) => stockItem.certificate_number);

            const config = {
                headers: process.env.ADMIN_PRODUCT_URL_KEY ? { Authorization: process.env.ADMIN_PRODUCT_URL_KEY } : {}
            };
            let apiRes: any = await axios.get(process.env.ADMIN_PRODUCT_URL ?? '', config);
            apiRes = apiRes?.data;

            if (apiRes) {
                let stockData;
                if (apiRes?.data) {
                    stockData = apiRes?.data;
                } else if (apiRes?.details) {
                    stockData = apiRes?.details;
                }

                if (stockData?.length) {
                    let cronSuccessful = true;
                    const cronErr: string[] = [];
                    let adminStockRes;
                    const chunkedArray: object[] = [];
                    const chunkSize = 10;
                    for (let i = 0; i < stockData.length; i += chunkSize) {
                        chunkedArray.push(stockData.slice(i, i + chunkSize));
                    }
                    for (const item of chunkedArray) {
                        try {
                            const payload = {
                                stock_array_object: item,
                                admin_id: adminObj.id
                            };

                            const addStockConfig = {
                                headers: {
                                    authorization: process.env.ADD_STOCK_AUTH
                                }
                            };

                            adminStockRes = await axios.post(process.env.ADD_STOCK_URL ?? '', payload, addStockConfig);
                            logger.info(`adminStockRes==>${JSON.stringify(adminStockRes.data)}`);
                        } catch (error: any) {
                            logger.error('error in ADD_STOCK_URL admin cron', error);
                            cronSuccessful = false;
                            cronErr.push(JSON.stringify(error?.response?.data ?? error));
                        }
                    }
                    try {
                        await models.cron_logs.create({
                            admin_id: adminObj.id,
                            is_successful: cronSuccessful,
                            data: JSON.stringify(stockData),
                            message: cronSuccessful ? JSON.stringify(adminStockRes.data) : JSON.stringify(cronErr),
                            type: 'cron'
                        });
                    } catch (err) {
                        logger.error('error in admin cron cron_logs create', err);
                    }

                    // const bulkCreateObject: object[] = [];
                    // await Promise.all(
                    //     await stockData.map(async (stockItem) => {
                    //         const stockCreateObject: any = await getStockModalFields(stockItem, 0, null, null, 0);
                    //         if (stockCreateObject) {
                    //             stockCreateObject.admin_id = adminObj.id;
                    //             bulkCreateObject.push(stockCreateObject);

                    //             if (stockIds.find((stocksItem) => stocksItem === stockCreateObject.stock_id)) {
                    //                 // update query base on stock_id
                    //                 await models.stocks.update(stockCreateObject, {
                    //                     where: {
                    //                         stock_id: stockCreateObject.stock_id,
                    //                         admin_id: adminObj.id,
                    //                         status: 'AVAILABLE'
                    //                     }
                    //                 });
                    //             } else if (
                    //                 stockCreateObject.certificate_number &&
                    //                 certificateNumbers.find(
                    //                     (stocksItem) => stocksItem === stockCreateObject.certificate_number
                    //                 )
                    //             ) {
                    //                 // update query base on certificate_number
                    //                 await models.stocks.update(stockCreateObject, {
                    //                     where: {
                    //                         certificate_number: stockCreateObject.certificate_number,
                    //                         admin_id: adminObj.id,
                    //                         status: 'AVAILABLE'
                    //                     }
                    //                 });
                    //             } else {
                    //                 bulkCreateObject.push(stockCreateObject);
                    //             }
                    //         }
                    //     })
                    // );

                    // await models.stocks.bulkCreate(bulkCreateObject);
                }
            }
        }
    }
};

export const saveCsvFile = async (jsonData: any, fileName: string) => {
    logger.info(`!!!!!!!!!!saveCsvFile function started!!!!!!!!!!!!`);
    const filePathToSaveCsv = path.join(__dirname, `../${fileName}`);
    logger.info(`!!!!!!!!!!!Saving Path!!!!!!!!!!!! ${filePathToSaveCsv}`);

    try {
        const csvData = parse(jsonData);
        fs.writeFileSync(filePathToSaveCsv, csvData, 'utf8');
        logger.info(`CSV file saved at ${filePathToSaveCsv}`);

        // const newPath = `/www/wwwroot/projects/diamond_company_api/public/stock/${fileName}`;
        const newPath = `${process.env.FTP_FILE_RENAME_PATH}/${fileName}`;
        logger.info(`!!!!!!!!newPath!!!!!!!!! ${newPath}`);

        await new Promise((resolve, reject) => {
            fs.rename(filePathToSaveCsv, newPath, (error: any) => {
                if (error) {
                    logger.error(`!!!!Error Renaming!!!!!!!!! ${error}`);
                    reject(error);
                }
                logger.info('SuccessFully Renamed');
                resolve(true);
            });
        });

        return newPath;
    } catch (error) {
        logger.error('Error saving CSV file', error);
        throw error;
    }
};

export const getStockData = async (vendorChunkData) => {
    try {
        for (const ele of vendorChunkData) {
            try {
                for (const item of ele) {
                    try {
                        logger.info(`!!!!!!!!!!Started For Vendor!!!!!!!!! ${item.dataValues.id}`);

                        const url = item.dataValues.api_url;
                        logger.info(`!!!URL!!! ${url}`);

                        if (!url) {
                            return;
                        }
                        const config = {
                            headers: item.dataValues.headers ? JSON.parse(item.dataValues.headers) : {}
                        };
                        const data =
                            typeof item.dataValues.body === 'string'
                                ? JSON.parse(item.dataValues.body)
                                : item.dataValues.body;

                        logger.info(`!!!config!!! ${JSON.stringify(config, null, 2)}`);
                        logger.info(`!!!data!!! ${JSON.stringify(data, null, 2)}`);
                        logger.info(`!!!Type!!! ${item.dataValues.api_type}`);

                        let apiRes;

                        if (item?.dataValues?.is_formdata_request) {
                            logger.info(`!!!!!!!!!FormData Call!!!!!!!!!1`);
                            const formData = new FormData();
                            for (const bodyItem of Object.keys(data)) {
                                formData.append(bodyItem, data[bodyItem]);
                            }

                            const response = await axios.request({
                                url,
                                method: item?.dataValues?.api_type || 'post',
                                data: formData,
                                headers: config.headers
                            });

                            apiRes = response?.data;
                        } else {
                            switch (String(item?.dataValues?.api_type).toLowerCase()) {
                                case 'get':
                                    apiRes = await axios.get(url, config);
                                    break;
                                case 'post':
                                    apiRes = await axios.post(url, data, config);
                                    break;
                                default:
                                    apiRes = await axios.get(url, config);
                                    break;
                            }
                            apiRes = apiRes?.data;
                        }

                        if (apiRes) {
                            let stockData;
                            if (apiRes?.data) {
                                stockData = apiRes?.data;
                            } else if (apiRes?.details) {
                                stockData = apiRes?.details;
                            } else if (apiRes?.GetStockResult?.Data) {
                                stockData = apiRes?.GetStockResult?.Data;
                            } else if (apiRes?.StoneList) {
                                stockData = apiRes?.StoneList;
                            } else if (apiRes?.Data) {
                                stockData = apiRes?.Data;
                            } else if (apiRes?.DataList) {
                                stockData = apiRes?.DataList;
                            } else if (apiRes?.UserData) {
                                stockData = apiRes?.UserData;
                            } else {
                                stockData = apiRes;
                            }

                            const typeOfStockData = typeof stockData;
                            logger.info(`!!!!!typeOfStockData!!!!!!!! ${typeOfStockData}`);
                            if (typeOfStockData === 'string') {
                                stockData = JSON.parse(stockData);
                                if (stockData?.ViPacketListForAPIResult) {
                                    stockData = stockData?.ViPacketListForAPIResult;
                                }
                            }

                            const cronSuccessful = true;
                            const cronErr: string[] = [];
                            logger.info(`!!!!!!!stockData Length!!!!!!!!! ${stockData?.length}`);

                            if (stockData?.length) {
                                try {
                                    const savedCsvPath = await saveCsvFile(
                                        stockData,
                                        `converted_file${Date.now()}.csv`
                                    );
                                    logger.info(`!!!!!!!savedCsvPath!!!!!!!!!!! ${savedCsvPath}`);

                                    const addStockPayload = {
                                        vendor_id: item.dataValues.id,
                                        filePath: savedCsvPath
                                    };

                                    const addCsvStockConfig = {
                                        headers: {
                                            authorization: process.env.ADD_STOCK_AUTH
                                        }
                                    };

                                    const res = await axios.post(
                                        process.env.ADD_STOCK_URL ?? '',
                                        addStockPayload,
                                        addCsvStockConfig
                                    );

                                    try {
                                        await models.cron_logs.create({
                                            vendor_id: item.dataValues.id,
                                            is_successful: cronSuccessful,
                                            data: `!!!!!!!!!Stock Data Length!!!!!!!! ${stockData?.length}`,
                                            message: cronSuccessful
                                                ? JSON.stringify(apiRes.data)
                                                : JSON.stringify(cronErr),
                                            type: 'cron'
                                        });
                                    } catch (err) {
                                        logger.error('error in user cron chunk creation logs', err);
                                        throw err;
                                    }
                                } catch (error) {
                                    logger.error('error in user cron chunk creation ', error);
                                    try {
                                        await models.cron_logs.create({
                                            vendor_id: item.dataValues.id,
                                            is_successful: false,
                                            data: `!!!!!!!!!Stock Data Length!!!!!!!! ${stockData?.length}`,
                                            message: JSON.stringify(error),
                                            type: 'cron'
                                        });
                                    } catch (err) {
                                        logger.error('error in user cron chunk creation logs', err);
                                    }
                                }
                            }
                        }
                        logger.info(`!!!!!!!!!!Completed For Vendor!!!!!!!!! ${item.dataValues.id}`);
                    } catch (error) {
                        logger.error('getStockData error==>', error);
                        try {
                            await models.cron_logs.create({
                                vendor_id: item.dataValues.id,
                                is_successful: false,
                                data: `!!!!!!!!!Stock Data Length!!!!!!!! 0`,
                                message: JSON.stringify(error),
                                type: 'cron'
                            });
                        } catch (err) {
                            logger.error('error in user cron chunk creation logs', err);
                        }
                    }
                }
            } catch (err) {
                logger.error('getStockData error+', err);
            }
        }
    } catch (error) {
        logger.error('error+', error);
    }
};

export const createChunkArray = (array) => {
    const chunkedArray: object[] = [];
    const chunkSize = 10;
    for (let i = 0; i < array.length; i += chunkSize) {
        chunkedArray.push(array.slice(i, i + chunkSize));
    }
    return chunkedArray;
};
