import { Router, IRouter } from 'express';
import vendor from '../../../controllers/admin/vendor';
const router: IRouter = Router();

// users table
router.post('/create-ftp-credentials', vendor.createFTPCredentials);

// change-ftp-password
router.post('/change-ftp-password', vendor.changeFTPCredentials);

// create-diamond-type-folder-script
router.post('/create-diamond-type-folder-script', vendor.createDiamondTypeFolderScript);

// test-ftp-folder-permissions
router.post('/test-ftp-folder-permissions', vendor.testFTPFolderPermissions);

export default router;
