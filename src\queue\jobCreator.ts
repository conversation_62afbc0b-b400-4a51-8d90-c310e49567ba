import { Worker, Queue } from 'bullmq'; // Import Job type for addBulk structure
import { redisConnection } from './../common/redisConnection';
import { TRIGGER_QUEUE_NAME, VENDOR_QUEUE_NAME } from './../common/queueNames';
import { VendorJobData } from './../common/jobData';
import { getAllVendorIds } from './../services/vendorService';
import { logger } from '../utils/logger';

// Queue to add individual vendor jobs to
const vendorQueue = new Queue<VendorJobData>(VENDOR_QUEUE_NAME, {
    connection: redisConnection
});

// Worker listening for the trigger job
const triggerWorker = new Worker(
    TRIGGER_QUEUE_NAME,
    async (job) => {
        logger.info(`[PID: ${process.pid}] Received trigger job: ${job.name} (ID: ${job.id})`);

        try {
            // 1. Fetch all vendor IDs
            const allVendorIds = await getAllVendorIds();
            if (!allVendorIds || allVendorIds.length === 0) {
                logger.warn('No vendor IDs found. Skipping job creation.');
                return;
            }
            logger.info(`Fetched ${allVendorIds.length} vendor IDs. Creating individual jobs using addBulk...`);

            // 2. Prepare bulk job data structure
            // Each element in the array represents a job to be added
            const jobsToAdd = allVendorIds.map((vendor) => {
                return {
                    name: `fetch-inventory-${vendor.id}`,
                    data: vendor,
                    opts: {
                        // Options specific to this job (applied during addBulk)
                        jobId: `fetch-${vendor.id}-${Date.now()}-${Math.floor(Math.random() * 10000)}`, // Optional: create a unique job ID if needed
                        removeOnComplete: 150,
                        removeOnFail: 150,
                        attempts: 3,
                        backoff: {
                            type: 'exponential',
                            delay: 5000
                        }
                    }
                };
            });

            // 3. Add all jobs in bulk
            logger.info(`Adding ${jobsToAdd.length} jobs via addBulk...`);
            const addedJobs = await vendorQueue.addBulk(jobsToAdd);
            logger.info(
                `Successfully added ${addedJobs.length} individual vendor jobs via addBulk to the queue '${VENDOR_QUEUE_NAME}'.`
            );
            // Note: addBulk returns an array of the Job instances created.
        } catch (error) {
            logger.error(`[PID: ${process.pid}] Error processing trigger job ${job.id}:`, error);
            // Re-throw the error to mark the job as failed
            throw error;
        }
    },
    { connection: redisConnection, concurrency: 1 } // Process one trigger job at a time
);

triggerWorker.on('completed', (job) => {
    logger.info(`[PID: ${process.pid}] Trigger job ${job.id} completed successfully.`);
});

triggerWorker.on('failed', (job, err) => {
    logger.error(`[PID: ${process.pid}] Trigger job ${job?.id} failed:`, err);
});

logger.info(
    `[PID: ${process.pid}] Job Creator Worker started. Waiting for trigger jobs on queue '${TRIGGER_QUEUE_NAME}'...`
);
