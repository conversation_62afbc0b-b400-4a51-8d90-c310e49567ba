import { Job as BullJob } from 'bullmq'; // Alias Job to avoid naming conflict if needed elsewhere
import { VendorJobData, VendorFetchResult } from './../common/jobData';
import { fetchVendorInventory } from './../services/vendorService';
import { logger } from '../utils/logger';

// This function processes *one* job (containing a single vendor ID)
export const processVendorInventoryJob = async (job: BullJob<VendorJobData>): Promise<VendorFetchResult> => {
    const vendor: VendorJobData = job.data; // Extract single vendor ID
    logger.info(`[PID: ${process.pid}] Worker processing job ${job.id} for vendor: ${vendor.id}`);

    try {
        // Directly fetch the inventory for this vendor
        const result = await fetchVendorInventory(vendor);

        if (!result.success) {
            logger.warn(
                `[PID: ${process.pid}] Job ${job.id} (Vendor: ${vendor.id}) completed with failure: ${result.message}`
            );
            // Potentially throw an error to trigger BullMQ retry based on queue settings
            // throw new Error(result.message || `Failed to fetch inventory for ${vendorId}`);
        } else {
            logger.info(`[PID: ${process.pid}] Job ${job.id} (Vendor: ${vendor.id}) completed successfully.`);
        }

        // Return the result (success or failure details)
        return result;
    } catch (error) {
        logger.error(`[PID: ${process.pid}] Unhandled error processing job ${job.id} (Vendor: ${vendor.id}):`, error);
        // Re-throw the error to mark the job as failed in BullMQ for potential retry
        throw error;
    }
};
