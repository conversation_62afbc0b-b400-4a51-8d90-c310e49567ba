import { logger } from '../../utils/logger';
import models from '../../models/index';
import { Request, Response } from 'express';
import { httpStatusCodes } from '../../utils/constants';
class User {
    constructor() {
        // super();
    }
    async listUsers(req: Request, res: Response) {
        logger.info('!!!!!!listUsers function start!!!!!');
        try {
            const userData = await models.vendors.findAll();
            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'successfully listed',
                data: userData
            });
            return;
        } catch (error: any) {
            logger.error(error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message: typeof error === 'string' ? error : typeof error.message === 'string' ? error.message : 500
            });
            return;
        }
    }
}

export default new User();
