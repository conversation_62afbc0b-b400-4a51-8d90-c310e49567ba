import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface ShipmentAttributes {
    id?: string; // id is an auto-generated UUID
    title: string;
    description: string;
    amount: number;
    countries: []; // india, china, pakistan
    is_default?: boolean;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface ShipmentCreationAttributes extends Optional<ShipmentAttributes, 'id'> {}

interface ShipmentInstance extends Model<ShipmentAttributes, ShipmentCreationAttributes>, ShipmentAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type ShipmentStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => ShipmentInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const shipments = sequelize.define<ShipmentInstance>(
        'shipments',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            title: {
                type: DataTypes.STRING,
                allowNull: false
            },
            description: {
                type: DataTypes.STRING,
                allowNull: false
            },
            amount: {
                type: DataTypes.DOUBLE,
                allowNull: false
            },
            countries: {
                type: DataTypes.ARRAY(DataTypes.STRING),
                allowNull: false
            },
            is_default: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as ShipmentStatic;

    // TODO: make common function to sync
    // await shipments.sync({ alter: true });

    return shipments;
};
