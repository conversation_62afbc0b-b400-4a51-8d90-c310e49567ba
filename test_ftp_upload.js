const ftp = require('basic-ftp');
const fs = require('fs');
const path = require('path');

async function testFTPUpload() {
    const client = new ftp.Client();
    client.ftp.verbose = true;
    
    try {
        console.log('Connecting to FTP server...');
        await client.access({
            host: "localhost",
            port: 6543,
            user: "admin",
            password: "admin",
            secure: false
        });
        
        console.log('Connected successfully!');
        console.log('Current directory:', await client.pwd());
        
        // List files in current directory
        console.log('\nListing files in current directory:');
        const list = await client.list();
        console.log(list);
        
        // Try to change to 'both' directory (this was failing in your original error)
        try {
            console.log('\nTrying to change to "both" directory...');
            await client.cd('both');
            console.log('Successfully changed to "both" directory');
            console.log('Current directory:', await client.pwd());
        } catch (cdError) {
            console.log('Failed to change to "both" directory:', cdError.message);
        }
        
        // Try to upload a test file
        try {
            console.log('\nCreating test file...');
            const testContent = 'This is a test file for FTP upload';
            const testFilePath = 'test_upload.txt';
            fs.writeFileSync(testFilePath, testContent);
            
            console.log('Uploading test file...');
            await client.uploadFrom(testFilePath, 'test_upload.txt');
            console.log('File uploaded successfully!');
            
            // Clean up local test file
            fs.unlinkSync(testFilePath);
            
        } catch (uploadError) {
            console.log('Upload failed:', uploadError.message);
        }
        
    } catch (err) {
        console.log('FTP connection failed:', err.message);
    } finally {
        client.close();
    }
}

console.log('Testing FTP Upload...');
testFTPUpload();
