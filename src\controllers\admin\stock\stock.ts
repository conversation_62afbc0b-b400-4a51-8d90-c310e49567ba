import { NextFunction, Request, Response } from 'express';
import models from '../../../models';
import { adminRole, httpStatusCodes, stockStatus } from '../../../utils/constants';
import { logger } from '../../../utils/logger';
import { Op } from 'sequelize';
import { StockAttributes } from '../../../models/stocks';
import { convertExcelToJson } from './readExcel';
import filters from './getFilterStockFields';
import { UploadConstant } from '../../../utils/upload_constant';

class Stock {
    /*
        --------------------------------------------------------------------------------
        Stock functions
    */

    /**
     * @api {get} /v1/auth/admin/stock-list
     * @apiName listStocks
     * @apiGroup AdminStocks
     *
     *
     * @apiSuccess {Object} Stocks.
     */
    async listStocks(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!listStocks function start!!!!!');
        try {
            const role = req[`role`];
            if (![adminRole.vendor, adminRole.superAdmin, adminRole.subAdmin].includes(role as adminRole)) {
                throw new Error('Unauthorized');
            }

            let skip: any = req.query.skip;
            let limit: any = req.query.limit;
            let isAdmin: boolean = (req.query.is_admin || false).toString().toLowerCase() === 'true';
            const filterObject = req.body.filterObject;
            const vendorId = req[`vendor`]?.id;

            if (vendorId) {
                isAdmin = false;
            }

            const conditions: any = [
                {
                    is_active: true
                },
                {
                    _deleted: false
                }
            ];

            if (skip && limit) {
                skip = parseInt(String(req.query.skip), 10);
            }
            if (skip && limit) {
                limit = parseInt(String(req.query.limit), 10);
            }

            if (filterObject) {
                const filterObjectArray = await filters.getFilters(filterObject);
                conditions.push({
                    [Op.and]: filterObjectArray
                });
            }

            if (isAdmin) {
                conditions.push({
                    admin_id: { [Op.ne]: null }
                });
            } else {
                if (vendorId) {
                    conditions.push({
                        vendor_id: vendorId
                    });
                } else {
                    conditions.push({
                        vendor_id: { [Op.ne]: null }
                    });
                }
            }

            const { rows, count } = await models.stocks.findAndCountAll({
                where: {
                    [Op.and]: conditions
                },
                order: [['createdAt', 'DESC']],
                offset: skip,
                limit
            });

            const vendorsIds = rows.map((stock: any) => stock.vendor_id);

            const vendors = await models.vendors.findAll({
                where: { id: { [Op.in]: vendorsIds } },
                attributes: ['id', 'first_name', 'last_name', 'email']
            });

            const stocksList = rows.map((stock: any) => ({
                ...JSON.parse(JSON.stringify(stock)),
                vendor_details: vendors.find((vendor: any) => vendor.id === stock.vendor_id)
            }));

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stocks successfully listed',
                data: stocksList,
                count
            });

            return;
        } catch (error: any) {
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/stock
     *  @apiName addStock
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async addStock(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!AddStock function start!!!!!');
        try {
            const role = req[`role`];
            if (![adminRole.vendor, adminRole.superAdmin].includes(role as adminRole)) {
                throw new Error('Unauthorized');
            }

            const fileName = req.body.file_name;
            const replaceAll = req.body.replace_all;
            const offset = req.body.offset || 0;
            // const filePath = `./public/stock/file_1711611076714.xlsx`;
            // const filePath = `${process.env.BASE_URL}/stock/${fileName}`;
            const filePath = `${UploadConstant.UPLOAD_DIR_STOCK}/${fileName}`;

            if (!fileName) {
                throw new Error('Filename required');
            }

            const adminId = req[`admin`]?.id;
            const vendorId = req[`vendor`]?.id;
            let vendorMargin = 0;

            if (vendorId) {
                const isVendorBlackListed = await models.vendors.findOne({
                    where: { id: vendorId }
                });

                if (isVendorBlackListed && isVendorBlackListed.is_blacklisted) {
                    throw new Error(`Couldn't create stock, Vendor blacklisted`);
                }

                vendorMargin = isVendorBlackListed.margin_percentage;
            }

            const stockData = await models.stocks.findAll({
                where: {
                    [Op.and]: [
                        {
                            [Op.or]: [adminId ? { admin_id: adminId } : {}, vendorId ? { vendor_id: vendorId } : {}]
                        },
                        {
                            _deleted: false
                        }
                    ]
                },
                attributes: ['stock_id', 'status', 'admin_id', 'vendor_id']
            });

            let stockIds = stockData?.map((stockItem: any) => stockItem.stock_id);

            if (replaceAll) {
                await models.stocks.destroy({
                    where: {
                        stock_id: { [Op.in]: stockIds },
                        status: stockStatus.available,
                        [Op.or]: [adminId ? { admin_id: adminId } : {}, vendorId ? { vendor_id: vendorId } : {}]
                    }
                });
                stockIds = stockData
                    ?.filter((stock: any) => stock.status !== stockStatus.available)
                    .map((stock: any) => stock.stock_id);
            }

            const totalResultArray: any = await convertExcelToJson(
                filePath,
                stockData,
                stockIds,
                offset,
                adminId,
                vendorId,
                vendorMargin
            );

            const bulkCreateObject = totalResultArray.map((item: any) =>
                adminId ? { ...item, admin_id: adminId } : { ...item, vendor_id: vendorId }
            );

            await models.stocks.bulkCreate(bulkCreateObject);

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock created successfully!'
                // data: stockResult
            });
        } catch (error: any) {
            next(error);
        }
    }

    /**
     *  @api {post} /v1/auth/admin/stock
     *  @apiName deleteStock
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async deleteStock(req: Request, res: Response, next: NextFunction) {
        logger.info('!!!!!!DeleteStock function start!!!!!');
        try {
            const id = req.query.id;

            const role = req[`role`];
            const vendorId = req[`vendor`]?.id;

            const stock = await models.stocks.findOne({
                where: {
                    id,
                    _deleted: false
                }
            });

            if (!stock) {
                throw new Error('Stock not found');
            }

            if (adminRole.vendor === role) {
                if (stock.vendor_id !== vendorId) {
                    throw new Error('Unauthorized access!!');
                }
            }

            await models.stocks.update({ _deleted: true }, { where: { id } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock deleted successfully!'
            });
        } catch (error: any) {
            next(error);
        }
    }

    /**
     *  @api {put} /v1/auth/admin/stock/status
     *  @apiName changeStockStatus
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async changeStockStatus(req: Request, res: Response, next: NextFunction) {
        try {
            const { id, is_active, is_featured, is_new_arrival } = req.body;

            const role = req[`role`];
            const vendorId = req[`vendor`]?.id;
            const updateObject: any = {};

            if (typeof is_active === 'boolean') {
                updateObject.is_active = is_active;
            } else if (typeof is_featured === 'boolean') {
                updateObject.is_featured = is_featured;
            } else if (typeof is_new_arrival === 'boolean') {
                updateObject.is_new_arrival = is_new_arrival;
            }

            if (Object.entries(updateObject).length === 0) {
                throw Error('Please provide all data');
            }

            const stock = await models.stocks.findOne({
                where: {
                    id,
                    _deleted: false
                }
            });

            if (!stock) {
                throw new Error('Stock not found');
            }

            if (adminRole.vendor === role) {
                if (stock.vendor_id !== vendorId) {
                    throw new Error('Unauthorized access!!');
                }
            }

            await models.stocks.update(updateObject, { where: { id } });

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock User Status'
            });

            return;
        } catch (error: any) {
            next(error);
        }
    }

    /**
     *  @api {get} /v1/auth/admin/stock-details
     *  @apiName stockDetails
     *  @apiGroup Stock
     *
     *  @apiSuccess {Object} Stock
     */
    async getStockDetails(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.query.id;

            const stock = await models.stocks.findOne({ where: { id } });

            if (!stock) {
                throw new Error(`Stock details not found`);
            }

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'Stock details listed successfully',
                data: stock
            });

            return;
        } catch (error: any) {
            next(error);
        }
    }
}

export default new Stock();
