import { logger } from '../../utils/logger';
import models from '../../models/index';
import { Request, Response } from 'express';
import { httpStatusCodes } from '../../utils/constants';
import { exec } from 'child_process';
import fs from 'fs';
import util from 'util';
import dotenv from 'dotenv';
import path from 'path';
dotenv.config({ path: path.join(__dirname, '../../../.env') });

class Vendor {
    constructor() {
        // super();
    }
    async createFTPCredentials(req: Request, res: Response) {
        logger.info('!!!!!!createFTPCredentials function start!!!!!');
        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_AUTH) {
                throw new Error(`Unauthorized access`);
            }

            const execPromise = util.promisify(exec);

            const { ftp_username, ftp_password, vendor_id } = req.body;

            if (!ftp_password || !ftp_username || !vendor_id) {
                throw new Error('Please provide all data!!!');
            }

            const dirPath = `${process.env.FTP_FOLDER_PATH}/clients/${vendor_id}`;

            logger.info(`path ${dirPath}`);

            fs.mkdirSync(dirPath, { recursive: true });

            // Create subdirectories: cvd, natural, both
            const cvdDir = `${dirPath}/cvd/`;
            const naturalDir = `${dirPath}/natural/`;
            const bothDir = `${dirPath}/both/`;

            fs.mkdirSync(cvdDir, { recursive: true });
            fs.mkdirSync(naturalDir, { recursive: true });
            fs.mkdirSync(bothDir, { recursive: true });

            // const cmdOne = await execPromise(`chown www.www ${path}`);
            const cmdOne = await execPromise(`chown -R www-data:www-data ${dirPath} && chmod -R 775 ${dirPath}`);

            if (cmdOne.stderr) {
                throw new Error(`${cmdOne.stderr}`);
            }

            const executePromise = `/www/server/pure-ftpd/bin/pure-pw useradd "${ftp_username}" -u www -d ${dirPath} <<EOF \n${ftp_password}\n${ftp_password}\nEOF`;
            logger.info(`executePromise ${executePromise}`);
            const cmdTwo = await execPromise(executePromise);

            if (cmdTwo.stderr) {
                throw new Error(`${cmdTwo.stderr}`);
            }

            const cmdThree = await execPromise(
                `/www/server/pure-ftpd/bin/pure-pw mkdb /www/server/pure-ftpd/etc/pureftpd.pdb`
            );

            if (cmdThree.stderr) {
                throw new Error(`${cmdThree.stderr}`);
            }

            logger.info('FTP Credentials Created SuccessFully');

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'FTP Credentials created successfully!!'
            });

            ///
            return;
        } catch (error: any) {
            logger.error(error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message: typeof error === 'string' ? error : typeof error.message === 'string' ? error.message : 500
            });
            return;
        }
    }

    /// change ftp password
    async changeFTPCredentials(req: Request, res: Response) {
        logger.info('!!!!!!changeFTPCredentials function start!!!!!');

        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_AUTH) {
                throw new Error('Unauthorized access');
            }

            const execPromise = util.promisify(exec);

            const { ftp_username, new_password } = req.body;

            if (!ftp_username || !new_password) {
                throw new Error('Please provide all data!!!');
            }

            // First, check if the user exists
            const listUsersCmd = `/www/server/pure-ftpd/bin/pure-pw list`;
            logger.info(`Checking if user exists: ${ftp_username}`);

            const userListResult = await execPromise(listUsersCmd);

            if (!userListResult.stdout.includes(ftp_username)) {
                throw new Error(`FTP user "${ftp_username}" does not exist. Please create the user first.`);
            }

            // Create a temporary script file to handle password input
            const scriptPath = `/tmp/change_ftp_password_${Date.now()}.sh`;
            const scriptContent = `#!/bin/bash
echo "${new_password}"
echo "${new_password}"
`;

            // Write the script file
            await fs.promises.writeFile(scriptPath, scriptContent, { mode: 0o755 });

            try {
                // Execute the password change command with the script
                const updatePasswordCmd = `${scriptPath} | /www/server/pure-ftpd/bin/pure-pw passwd "${ftp_username}" 2>&1`;
                logger.info(`updatePasswordCmd ${updatePasswordCmd}`);

                const cmdOne = await execPromise(updatePasswordCmd);

                // Check for real errors, ignore warnings about invalid lines
                if (
                    cmdOne.stderr &&
                    !cmdOne.stderr.includes('Warning: invalid line') &&
                    !cmdOne.stdout.includes('Password changed') &&
                    cmdOne.stderr.trim() !== ''
                ) {
                    throw new Error(`${cmdOne.stderr}`);
                }

                logger.info(`Password change result: stdout=${cmdOne.stdout}, stderr=${cmdOne.stderr}`);
            } finally {
                // Clean up the temporary script file
                try {
                    await fs.promises.unlink(scriptPath);
                } catch (unlinkError) {
                    logger.warn(`Failed to delete temporary script: ${unlinkError}`);
                }
            }

            // Rebuild the Pure-FTPd database
            const cmdTwo = await execPromise(
                `/www/server/pure-ftpd/bin/pure-pw mkdb /www/server/pure-ftpd/etc/pureftpd.pdb 2>&1`
            );

            // Check for real errors in database rebuild, ignore warnings about invalid lines
            if (cmdTwo.stderr && !cmdTwo.stderr.includes('Warning: invalid line') && cmdTwo.stderr.trim() !== '') {
                throw new Error(`Database rebuild failed: ${cmdTwo.stderr}`);
            }

            logger.info(`Database rebuild result: stdout=${cmdTwo.stdout}, stderr=${cmdTwo.stderr}`);

            logger.info('FTP Password Updated Successfully');

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'FTP Password Updated Successfully!!'
            });
        } catch (error: any) {
            logger.error(error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message: typeof error === 'string' ? error : typeof error.message === 'string' ? error.message : 500
            });
        }
    }

    async createDiamondTypeFolderScript(req: Request, res: Response) {
        logger.info('!!!!!!createDiamondTypeFolderScript function start!!!!!');
        try {
            const authorization = req.headers.authorization;
            if (authorization !== process.env.ADD_STOCK_AUTH) {
                throw new Error(`Unauthorized access`);
            }

            const execPromise = util.promisify(exec);
            const isWindows = process.platform === 'win32';

            const vendors = await models.vendors.findAll({
                where: {
                    _deleted: false,
                    is_active: true
                },
                attributes: ['id']
            });

            const vendorIds: string[] = vendors.map((vendor: { id: string }) => vendor.id);
            logger.info(`Vendor IDs: ${vendorIds.join(', ')}`);
            logger.info(`Operating System: ${process.platform}`);
            logger.info(`FTP Folder Path: ${process.env.FTP_FOLDER_PATH}`);

            for (const vendorId of vendorIds) {
                // Use the same path structure as the FTP server expects
                const dirPath = path.join(process.env.FTP_FOLDER_PATH!, 'clients', vendorId);
                const relativePath = path.join('public', 'stocks', 'clients', vendorId);

                logger.info(`Creating directories under path: ${dirPath}`);
                logger.info(`FTP server expects relative path: ${relativePath}`);

                // Ensure both absolute and relative paths point to the same location
                const resolvedAbsolute = path.resolve(dirPath);
                const resolvedRelative = path.resolve(relativePath);

                if (resolvedAbsolute !== resolvedRelative) {
                    logger.warn(`Path mismatch detected!`);
                    logger.warn(`Absolute path resolves to: ${resolvedAbsolute}`);
                    logger.warn(`Relative path resolves to: ${resolvedRelative}`);
                    logger.warn(`Using absolute path for folder creation: ${dirPath}`);
                }

                // Ensure the main vendor directory exists first
                if (!fs.existsSync(dirPath)) {
                    fs.mkdirSync(dirPath, { recursive: true });
                    logger.info(`Created main vendor directory: ${dirPath}`);
                }

                const subDirs = ['cvd', 'natural', 'both'];

                for (const sub of subDirs) {
                    const fullSubDir = path.join(dirPath, sub);

                    // Create directory if it doesn't exist
                    if (!fs.existsSync(fullSubDir)) {
                        fs.mkdirSync(fullSubDir, { recursive: true });
                        logger.info(`Created: ${fullSubDir}`);
                    } else {
                        logger.info(`Skipped (already exists): ${fullSubDir}`);
                    }

                    // Set permissions based on operating system
                    try {
                        if (isWindows) {
                            // Windows: Use icacls to grant full permissions to Everyone
                            await execPromise(`icacls "${fullSubDir}" /grant Everyone:(OI)(CI)(F) /T`);
                            logger.info(`Granted Windows permissions for ${fullSubDir}`);
                        } else {
                            // Linux/Unix: Use chmod to set 777 permissions (read/write/execute for all)
                            await execPromise(`chmod -R 777 "${fullSubDir}"`);
                            logger.info(`Granted Unix permissions for ${fullSubDir}`);
                        }
                    } catch (permErr: any) {
                        logger.warn(`Failed to set permissions on ${fullSubDir}:`, permErr.message || permErr);

                        // Try alternative permission setting
                        try {
                            if (isWindows) {
                                // Try with Users group instead of Everyone
                                await execPromise(`icacls "${fullSubDir}" /grant Users:(OI)(CI)(F) /T`);
                                logger.info(`Granted Windows Users permissions for ${fullSubDir}`);
                            } else {
                                // Try with chown if chmod failed
                                await execPromise(`chown -R $(whoami) "${fullSubDir}"`);
                                await execPromise(`chmod -R 755 "${fullSubDir}"`);
                                logger.info(`Applied alternative Unix permissions for ${fullSubDir}`);
                            }
                        } catch (altPermErr: any) {
                            logger.error(`All permission attempts failed for ${fullSubDir}:`, altPermErr.message || altPermErr);
                        }
                    }
                }

                // Apply permissions to main vendor folder and its parent
                try {
                    const parentDir = path.join(process.env.FTP_FOLDER_PATH!, 'clients');

                    if (isWindows) {
                        await execPromise(`icacls "${dirPath}" /grant Everyone:(OI)(CI)(F) /T`);
                        await execPromise(`icacls "${parentDir}" /grant Everyone:(OI)(CI)(F) /T`);
                        logger.info(`Granted Windows permissions to ${dirPath} and parent`);
                    } else {
                        await execPromise(`chmod -R 777 "${dirPath}"`);
                        await execPromise(`chmod -R 777 "${parentDir}"`);
                        logger.info(`Granted Unix permissions to ${dirPath} and parent`);
                    }
                } catch (parentPermErr: any) {
                    logger.warn(`Failed to apply parent permissions:`, parentPermErr.message || parentPermErr);
                }

                // Verify directory structure
                try {
                    const stats = fs.statSync(dirPath);
                    logger.info(`Vendor ${vendorId} directory verified - isDirectory: ${stats.isDirectory()}`);

                    for (const sub of subDirs) {
                        const fullSubDir = path.join(dirPath, sub);
                        const subStats = fs.statSync(fullSubDir);
                        logger.info(`  - ${sub} subdirectory verified - isDirectory: ${subStats.isDirectory()}`);
                    }
                } catch (verifyErr: any) {
                    logger.error(`Failed to verify directory structure for vendor ${vendorId}:`, verifyErr.message);
                }

                logger.info(`Diamond Type Folders Ready for Vendor ${vendorId}`);
            }

            logger.info('All vendor folders created/verified successfully.');
            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'FTP Diamond Type Folders created or already existed for all vendors!'
            });
        } catch (error: any) {
            logger.error('Error in createDiamondTypeFolderScript:', error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message:
                    typeof error === 'string'
                        ? error
                        : typeof error.message === 'string'
                            ? error.message
                            : 'Internal Server Error'
            });
        }
    }

    async testFTPFolderPermissions(req: Request, res: Response) {
        logger.info('!!!!!!testFTPFolderPermissions function start!!!!!');
        try {
            const authorization = req.headers.authorization;
            if (authorization !== process.env.ADD_STOCK_AUTH) {
                throw new Error(`Unauthorized access`);
            }

            const { vendor_id } = req.body;
            if (!vendor_id) {
                throw new Error('Please provide vendor_id');
            }

            const testResults: any = {};

            // Test absolute path (used by folder creation script)
            const absolutePath = path.join(process.env.FTP_FOLDER_PATH!, 'clients', vendor_id);
            testResults.absolutePath = absolutePath;
            testResults.absolutePathExists = fs.existsSync(absolutePath);

            // Test relative path (used by FTP server)
            const relativePath = path.join('public', 'stocks', 'clients', vendor_id);
            testResults.relativePath = relativePath;
            testResults.relativePathExists = fs.existsSync(relativePath);

            // Test current working directory
            testResults.currentWorkingDirectory = process.cwd();

            // Test if paths are the same
            const resolvedAbsolute = path.resolve(absolutePath);
            const resolvedRelative = path.resolve(relativePath);
            testResults.pathsMatch = resolvedAbsolute === resolvedRelative;

            // Test subdirectories
            const subDirs = ['cvd', 'natural', 'both'];
            testResults.subdirectories = {};

            for (const sub of subDirs) {
                const absoluteSubPath = path.join(absolutePath, sub);
                const relativeSubPath = path.join(relativePath, sub);

                testResults.subdirectories[sub] = {
                    absolutePath: absoluteSubPath,
                    absoluteExists: fs.existsSync(absoluteSubPath),
                    relativePath: relativeSubPath,
                    relativeExists: fs.existsSync(relativeSubPath)
                };

                // Test write permissions by creating a test file
                if (fs.existsSync(absoluteSubPath)) {
                    try {
                        const testFile = path.join(absoluteSubPath, 'test_write_permission.txt');
                        fs.writeFileSync(testFile, 'test content');
                        fs.unlinkSync(testFile); // Clean up
                        testResults.subdirectories[sub].writePermission = true;
                    } catch (writeErr: any) {
                        testResults.subdirectories[sub].writePermission = false;
                        testResults.subdirectories[sub].writeError = writeErr.message || writeErr;
                    }
                }
            }

            // Test environment variables
            testResults.environment = {
                FTP_FOLDER_PATH: process.env.FTP_FOLDER_PATH,
                FTP_SERVER_URL: process.env.FTP_SERVER_URL,
                FTP_PORT: process.env.FTP_PORT,
                platform: process.platform
            };

            logger.info('FTP Folder Permission Test Results:', JSON.stringify(testResults, null, 2));

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'FTP folder permission test completed',
                data: testResults
            });
        } catch (error: any) {
            logger.error('Error in testFTPFolderPermissions:', error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message: typeof error === 'string' ? error : error.message || 'Internal Server Error'
            });
        }
    }
}
export default new Vendor();
