import { logger } from '../../utils/logger';
import models from '../../models/index';
import { Request, Response } from 'express';
import { httpStatusCodes } from '../../utils/constants';
import { exec } from 'child_process';
import fs from 'fs';
import util from 'util';
import dotenv from 'dotenv';
import path from 'path';
dotenv.config({ path: path.join(__dirname, '../../../.env') });

class Vendor {
    constructor() {
        // super();
    }
    async createFTPCredentials(req: Request, res: Response) {
        logger.info('!!!!!!createFTPCredentials function start!!!!!');
        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_AUTH) {
                throw new Error(`Unauthorized access`);
            }

            const execPromise = util.promisify(exec);

            const { ftp_username, ftp_password, vendor_id } = req.body;

            if (!ftp_password || !ftp_username || !vendor_id) {
                throw new Error('Please provide all data!!!');
            }

            const dirPath = `${process.env.FTP_FOLDER_PATH}/clients/${vendor_id}`;

            logger.info(`path ${dirPath}`);

            fs.mkdirSync(dirPath, { recursive: true });

            // Create subdirectories: cvd, natural, both
            const cvdDir = `${dirPath}/cvd/`;
            const naturalDir = `${dirPath}/natural/`;
            const bothDir = `${dirPath}/both/`;

            fs.mkdirSync(cvdDir, { recursive: true });
            fs.mkdirSync(naturalDir, { recursive: true });
            fs.mkdirSync(bothDir, { recursive: true });

            // const cmdOne = await execPromise(`chown www.www ${path}`);
            const cmdOne = await execPromise(`chown -R www-data:www-data ${dirPath} && chmod -R 775 ${dirPath}`);

            if (cmdOne.stderr) {
                throw new Error(`${cmdOne.stderr}`);
            }

            const executePromise = `/www/server/pure-ftpd/bin/pure-pw useradd "${ftp_username}" -u www -d ${dirPath} <<EOF \n${ftp_password}\n${ftp_password}\nEOF`;
            logger.info(`executePromise ${executePromise}`);
            const cmdTwo = await execPromise(executePromise);

            if (cmdTwo.stderr) {
                throw new Error(`${cmdTwo.stderr}`);
            }

            const cmdThree = await execPromise(
                `/www/server/pure-ftpd/bin/pure-pw mkdb /www/server/pure-ftpd/etc/pureftpd.pdb`
            );

            if (cmdThree.stderr) {
                throw new Error(`${cmdThree.stderr}`);
            }

            logger.info('FTP Credentials Created SuccessFully');

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'FTP Credentials created successfully!!'
            });

            ///
            return;
        } catch (error: any) {
            logger.error(error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message: typeof error === 'string' ? error : typeof error.message === 'string' ? error.message : 500
            });
            return;
        }
    }

    /// change ftp password
    async changeFTPCredentials(req: Request, res: Response) {
        logger.info('!!!!!!changeFTPCredentials function start!!!!!');

        try {
            const authorization = req.headers.authorization;

            if (authorization !== process.env.ADD_STOCK_AUTH) {
                throw new Error('Unauthorized access');
            }

            const execPromise = util.promisify(exec);

            const { ftp_username, new_password } = req.body;

            if (!ftp_username || !new_password) {
                throw new Error('Please provide all data!!!');
            }

            // First, check if the user exists
            const listUsersCmd = `/www/server/pure-ftpd/bin/pure-pw list`;
            logger.info(`Checking if user exists: ${ftp_username}`);

            const userListResult = await execPromise(listUsersCmd);

            if (!userListResult.stdout.includes(ftp_username)) {
                throw new Error(`FTP user "${ftp_username}" does not exist. Please create the user first.`);
            }

            // Create a temporary script file to handle password input
            const scriptPath = `/tmp/change_ftp_password_${Date.now()}.sh`;
            const scriptContent = `#!/bin/bash
echo "${new_password}"
echo "${new_password}"
`;

            // Write the script file
            await fs.promises.writeFile(scriptPath, scriptContent, { mode: 0o755 });

            try {
                // Execute the password change command with the script
                const updatePasswordCmd = `${scriptPath} | /www/server/pure-ftpd/bin/pure-pw passwd "${ftp_username}" 2>&1`;
                logger.info(`updatePasswordCmd ${updatePasswordCmd}`);

                const cmdOne = await execPromise(updatePasswordCmd);

                // Check for real errors, ignore warnings about invalid lines
                if (
                    cmdOne.stderr &&
                    !cmdOne.stderr.includes('Warning: invalid line') &&
                    !cmdOne.stdout.includes('Password changed') &&
                    cmdOne.stderr.trim() !== ''
                ) {
                    throw new Error(`${cmdOne.stderr}`);
                }

                logger.info(`Password change result: stdout=${cmdOne.stdout}, stderr=${cmdOne.stderr}`);
            } finally {
                // Clean up the temporary script file
                try {
                    await fs.promises.unlink(scriptPath);
                } catch (unlinkError) {
                    logger.warn(`Failed to delete temporary script: ${unlinkError}`);
                }
            }

            // Rebuild the Pure-FTPd database
            const cmdTwo = await execPromise(
                `/www/server/pure-ftpd/bin/pure-pw mkdb /www/server/pure-ftpd/etc/pureftpd.pdb 2>&1`
            );

            // Check for real errors in database rebuild, ignore warnings about invalid lines
            if (cmdTwo.stderr && !cmdTwo.stderr.includes('Warning: invalid line') && cmdTwo.stderr.trim() !== '') {
                throw new Error(`Database rebuild failed: ${cmdTwo.stderr}`);
            }

            logger.info(`Database rebuild result: stdout=${cmdTwo.stdout}, stderr=${cmdTwo.stderr}`);

            logger.info('FTP Password Updated Successfully');

            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'FTP Password Updated Successfully!!'
            });
        } catch (error: any) {
            logger.error(error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message: typeof error === 'string' ? error : typeof error.message === 'string' ? error.message : 500
            });
        }
    }

    async createDiamondTypeFolderScript(req: Request, res: Response) {
        logger.info('!!!!!!createDiamondTypeFolderScript function start!!!!!');
        try {
            const FTP_USER = 'Everyone';

            const authorization = req.headers.authorization;
            if (authorization !== process.env.ADD_STOCK_AUTH) {
                throw new Error(`Unauthorized access`);
            }

            const execPromise = util.promisify(exec);

            const vendors = await models.vendors.findAll({
                where: {
                    _deleted: false,
                    is_active: true
                },
                attributes: ['id']
            });

            const vendorIds: string[] = vendors.map((vendor: { id: string }) => vendor.id);
            logger.info(`Vendor IDs: ${vendorIds.join(', ')}`);

            for (const vendorId of vendorIds) {
                const dirPath = path.join(process.env.FTP_FOLDER_PATH!, 'clients', vendorId);
                logger.info(`Creating directories under path: ${dirPath}`);

                const subDirs = ['cvd', 'natural', 'both'];

                for (const sub of subDirs) {
                    const fullSubDir = path.join(dirPath, sub);
                    if (!fs.existsSync(fullSubDir)) {
                        fs.mkdirSync(fullSubDir, { recursive: true });
                        logger.info(`Created: ${fullSubDir}`);
                    } else {
                        logger.info(`Skipped (already exists): ${fullSubDir}`);
                    }

                    // ✅ Grant NTFS permissions using icacls
                    try {
                        await execPromise(`icacls "${fullSubDir}" /grant ${FTP_USER}:(OI)(CI)(M) /T`);
                        logger.info(`Granted permissions for ${fullSubDir}`);
                    } catch (permErr) {
                        logger.warn(`Failed to set NTFS permissions on ${fullSubDir}:`, permErr);
                    }
                }

                // Apply permissions to main vendor folder and its parent
                try {
                    await execPromise(`icacls "${dirPath}" /grant ${FTP_USER}:(OI)(CI)(M) /T`);
                    const parentDir = path.join(process.env.FTP_FOLDER_PATH!, 'clients');
                    await execPromise(`icacls "${parentDir}" /grant ${FTP_USER}:(OI)(CI)(M) /T`);
                    logger.info(`Granted permissions to ${dirPath} and parent`);
                } catch (parentPermErr) {
                    logger.warn(`Failed to apply parent permissions:`, parentPermErr);
                }

                logger.info(`Diamond Type Folders Ready for Vendor ${vendorId}`);
            }

            logger.info('All vendor folders created/verified successfully.');
            res.json({
                status: httpStatusCodes.SUCCESS_CODE,
                message: 'FTP Diamond Type Folders created or already existed for all vendors!'
            });
        } catch (error: any) {
            logger.error('Error in createDiamondTypeFolderScript:', error);
            res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message:
                    typeof error === 'string'
                        ? error
                        : typeof error.message === 'string'
                        ? error.message
                        : 'Internal Server Error'
            });
        }
    }
}
export default new Vendor();
