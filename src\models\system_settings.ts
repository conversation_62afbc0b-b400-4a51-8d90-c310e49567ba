import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface SystemSettingAttributes {
    id: string; // id is an auto-generated UUID
    billing_details: object;
    return_within: number; // days
    updatedAt?: Date;
}

interface SystemSettingCreationAttributes extends Optional<SystemSettingAttributes, 'id'> {}

interface SystemSettingInstance
    extends Model<SystemSettingAttributes, SystemSettingCreationAttributes>,
        SystemSettingAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type SystemSettingStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => SystemSettingInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const systemSettings = sequelize.define<SystemSettingInstance>(
        'system-settings',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            billing_details: {
                type: DataTypes.JSONB,
                allowNull: false,
                defaultValue: {}
            },
            return_within: {
                type: DataTypes.INTEGER,
                allowNull: true
            }
        },
        {
            freezeTableName: true
        }
    ) as SystemSettingStatic;
    //
    // await systemSettings.sync({ alter: true })

    return systemSettings;
};
