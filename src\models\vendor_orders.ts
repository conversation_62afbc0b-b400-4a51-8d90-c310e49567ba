import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface VendorOrderAttributes {
    id?: string;
    order_id: string;
    buy_request_id: string;
    vendor_id: string;
    stock_id: string;
    status: string; // pending, shipped, paid
    dollar_rate: number;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface VendorOrderCreationAttributes extends Optional<VendorOrderAttributes, 'id'> {}

interface VendorOrderInstance
    extends Model<VendorOrderAttributes, VendorOrderCreationAttributes>,
        VendorOrderAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type VendorOrderStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => VendorOrderInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const vendor_orders = sequelize.define<VendorOrderInstance>(
        'vendor_orders',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            order_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            vendor_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            buy_request_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            stock_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            status: {
                type: DataTypes.ENUM('PENDING', 'SHIPPED', 'PAID'),
                allowNull: false
            },
            dollar_rate: {
                type: DataTypes.DOUBLE,
                allowNull: true
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as VendorOrderStatic;

    vendor_orders.associate = (models) => {
        vendor_orders.belongsTo(models.orders, {
            foreignKey: 'order_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        vendor_orders.belongsTo(models.vendors, {
            foreignKey: 'vendor_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        vendor_orders.belongsTo(models.stocks, {
            foreignKey: 'stock_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    // TODO: make common function to sync
    // await vendor_orders.sync({ alter: true });

    return vendor_orders;
};
