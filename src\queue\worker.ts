import { Worker } from 'bullmq';
import { redisConnection } from './../common/redisConnection';
import { VENDOR_QUEUE_NAME } from './../common/queueNames';
import { VendorJobData, VendorFetchResult } from './../common/jobData';
import { processVendorInventoryJob } from './vendorInventoryProcessor';
import { logger } from '../utils/logger';

logger.info(`[PID: ${process.pid}] Starting Vendor Inventory Worker...`);

// Create a worker instance.
// Each instance running this file will connect to Redis and process jobs.
const worker = new Worker<VendorJobData, VendorFetchResult>( // Specify Job Data and Return Type
    VENDOR_QUEUE_NAME, // Listen to the queue with single-vendor jobs
    processVendorInventoryJob, // The async function that processes each vendor job
    {
        connection: redisConnection,
        // *** Concurrency set to 15 ***
        // Each worker process can handle up to 15 vendor API calls concurrently.
        concurrency: 5,
        limiter: {
            // Optional: Rate limiting per worker process
            max: 15, // Max 15 jobs processed by this worker
            duration: 1000 // per second. Adjust based on vendor API limits.
            // Note: Global limit across 3 workers would be 3 * 15 = 45/sec without further coordination.
        }
    }
);

worker.on('completed', (job, result) => {
    // Log based on the success flag in the result
    if (result.success) {
        logger.info(`[PID: ${process.pid}] Job ${job.id} (Vendor: ${job.data.id}) completed successfully.`);
    } else {
        // Already logged failure in processVendorInventoryJob, could add more details here if needed
        // console.warn(`[PID: ${process.pid}] Job ${job.id} (Vendor: ${job.data.vendorId}) completed with reported failure.`);
    }
});

worker.on('failed', (job, err) => {
    // This catches errors thrown from processVendorInventoryJob or internal BullMQ errors
    logger.error(
        `[PID: ${process.pid}] Job ${job?.id} (Vendor: ${job?.data?.id}) failed after ${job?.attemptsMade} attempts:`,
        err.message
    );
    // Add more detailed logging or alerting here
});

worker.on('error', (err) => {
    // Local worker errors (e.g., Redis connection issue)
    logger.error(`[PID: ${process.pid}] Worker error:`, err);
});

worker.on('active', (job) => {
    logger.info(`[PID: ${process.pid}] Job ${job.id} (Vendor: ${job.data.id}) started processing.`);
});

logger.info(
    `[PID: ${process.pid}] Vendor Inventory Worker listening on queue '${VENDOR_QUEUE_NAME}' with concurrency ${worker.opts.concurrency}`
);

// Graceful shutdown handling (No change needed)
const gracefulShutdown = async () => {
    logger.info(`[PID: ${process.pid}] Shutting down worker...`);
    await worker.close();
    logger.info(`[PID: ${process.pid}] Worker closed.`);
    process.exit(0);
};

process.on('SIGTERM', gracefulShutdown); // Signal typically sent by PM2 or Kubernetes
process.on('SIGINT', gracefulShutdown); // Signal typically sent by Ctrl+C
