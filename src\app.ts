import express, { Request<PERSON>and<PERSON> } from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import json2xls from 'json2xls';
import apiV1UnAuthRoutes from './routes/unAuthRoute';
import apiV1AuthRoutes from './routes/authRoute';
import { logger, accessSuccessLogger, accessErrorLogger } from './utils/logger';
import { tokenHandler } from './middlewares';
import multer from 'multer';
import path from 'path';
import { httpStatusCodes } from './utils/constants';
import chokidar from 'chokidar';
import { AdminProductsCron, productsCron } from './controllers/ProductCron';
import { processFileChange } from './controllers/productFileWatch';
import cron from 'node-cron';
import FTPSrv from 'ftp-srv';
import dotenv from 'dotenv';
import models from './models';
import bcrypt from 'bcrypt';
import { convertMelleExcelToJson } from './controllers/admin/melle/readMelleExcel';
import { convertStockMarginExcelToJson } from './controllers/admin/stock_margin/readMarginExcel';

dotenv.config({ path: path.join(__dirname, '../.env') });

const upload = multer();

const app = express();

// register loggers
app.use(accessSuccessLogger);
app.use(accessErrorLogger);
app.use(express.static(path.join(__dirname, 'public')));

app.disable('x-powered-by');
app.use(express.json() as RequestHandler);
app.use(express.urlencoded({ extended: true }) as RequestHandler);
app.use(json2xls.middleware);

global.stocksUpdatedId = [];
global.isMelleUploading = false;

app.use(
    cors({
        credentials: true,
        origin: [
            'http://localhost:3000',
            'https://brands.wherehouse.io',
            'https://wherehouse-seller-staging.web.app',
            'https://seller-staging-test.web.app'
        ],
        methods: ['GET', 'POST', 'PUT', 'DELETE']
    })
);
app.use(cookieParser('CookieSecret'));

app.all('/*', (req, res, next) => {
    // 	// CORS headers
    res.header('Access-Control-Allow-Origin', '*'); // restrict it to the required domain
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    // Set custom headers for CORS
    res.header('Access-Control-Allow-Headers', 'Content-type,Accept,X-Access-Token,X-Key,Authorization,Client-Key');

    if (req.method === 'OPTIONS') {
        res.status(httpStatusCodes.SUCCESS_CODE).end();
    } else {
        next();
    }
});

// parse FormData
app.use(upload.array());

app.use('/api/v1', apiV1UnAuthRoutes);
app.use('/api/v1/auth', tokenHandler, apiV1AuthRoutes);

// global error handler
app.use((err, req, res, next) => {
    logger.error(err.stack);
    res.status(500).json('Ouch! Something broke!');
    next();
});

// cron.schedule('1 0 * * *', productsCron, {
//     timezone: 'Asia/Kolkata' // ensures it runs 12:01 AM IST regardless of server timezone
// });
// cron.schedule('0 0,8,16 * * *', productsCron, {
//     timezone: 'Asia/Kolkata'
// });

// cron.schedule('1 0 * * *', AdminProductsCron);

setTimeout(() => {
    // AdminProductsCron();
    // productsCron();
});

const directoryPath = path.join(__dirname, '../public/stocks');

// Initialize watcher
const watcher = chokidar.watch(directoryPath, {
    persistent: true,
    ignoreInitial: true, // Process existing files on startup
    followSymlinks: true,
    cwd: directoryPath,
    disableGlobbing: true,
    depth: 3,
    atomic: 1000,
    awaitWriteFinish: true
});

async function handleFileChange(filePath) {
    try {
        logger.info(`handleFileChange ${filePath}`);
        if (
            path.extname(filePath) === '.xlsx' ||
            path.extname(filePath) === '.xls' ||
            path.extname(filePath) === '.csv'
        ) {
            if (filePath.split('/')[0] === 'admin') {
                logger.info(`Admin Side ${process.env.ADMIN_EMAIL} and ${process.env.ADMIN_PASS}`);
                const adminObj = await models.admins.findOne({
                    where: { email: process.env.ADMIN_EMAIL }
                });
                if (adminObj) {
                    const match = await bcrypt.compare(process.env.ADMIN_PASS, adminObj.password);
                    logger.info(`Match ${match}`);
                    if (match) {
                        logger.info(`~~~~~~~Global~~~~~~ ${global.stocksUpdatedId} and ${adminObj.id}`);
                        const findObject = global.stocksUpdatedId?.find((item) => item === adminObj.id);
                        logger.info(`!!!!findObject!!!! ${findObject}`);
                        if (!findObject) {
                            global.stocksUpdatedId.push(adminObj.id);
                            logger.info(`~~~~~~~Global After~~~~~~ ${global.stocksUpdatedId}`);
                            const result = await processFileChange(
                                null,
                                adminObj.id,
                                path.normalize(directoryPath + '/' + filePath),
                                false
                            );
                            logger.info(`!!!!!!!!!!!!!!!!Done!!!!!!!!!!!!!!`);
                            global.stocksUpdatedId = global.stocksUpdatedId.filter((item) => item !== adminObj.id);
                            logger.info(`~~~~~~~Global After Done~~~~~~ ${global.stocksUpdatedId}`);
                        }
                    }
                }
            } else {
                logger.info('Vendor Side');

                const segments = filePath.split('/');

                const vendorId = segments[1];

                const diamondType = segments?.length >= 4 ? segments[2] : '';

                logger.info(`ftp file path ${filePath}`);
                /// ftp file path clients/92bb4a7e-4403-4334-9527-c693bda6cfd3/natural/Vendor1.xlsx

                logger.info(`Vendor Id ${vendorId}`);

                logger.info(`diamond type ${diamondType}`);

                /// throw error id diamond type is empty
                if (diamondType === '') {
                    throw new Error('Diamond type is empty');
                }

                if (vendorId) {
                    logger.info(`~~~~~~~Global~~~~~~ ${global.stocksUpdatedId} and ${vendorId}`);
                    const findObject = global.stocksUpdatedId?.find((item) => item === vendorId);
                    logger.info(`!!!!findObject!!!! ${findObject}`);
                    if (!findObject) {
                        logger.info(`~~~~~~~Global After~~~~~~ ${global.stocksUpdatedId}`);
                        // TODO: as we now directly save the csv file into diamond_company_api project so it will be updated every time we got the notification for file change
                        // global.stocksUpdatedId.push(vendorId);
                        await processFileChange(
                            vendorId,
                            null,
                            path.normalize(directoryPath + '/' + filePath),
                            diamondType
                        );
                        logger.info(`!!!!!!!!!!!!!!!!Done!!!!!!!!!!!!!!`);
                        global.stocksUpdatedId = global.stocksUpdatedId.filter((item) => item !== vendorId);
                        logger.info(`~~~~~~~Global After Done~~~~~~ ${global.stocksUpdatedId}`);
                    }
                }
            }
        }
    } catch (error) {
        logger.error(`error=> ${error} filePath ${filePath}`);
    }
}
// Add event listeners
watcher
    .on('add', handleFileChange)
    .on('change', handleFileChange)
    .on('error', (error) => logger.error(`Watcher error: ${error}`));

/// watcher for melle
const melleDirectoryPath = path.join(__dirname, '../public/melle');

// Initialize watcher
const melleWatcher = chokidar.watch(melleDirectoryPath, {
    persistent: true,
    ignoreInitial: true, // Process existing files on startup
    followSymlinks: true,
    cwd: directoryPath,
    disableGlobbing: true,
    depth: 3,
    atomic: 1000
});

async function handleMelleFileChange(filePath) {
    try {
        logger.info(`handleMelleFileChange ${filePath}`);
        if (
            path.extname(filePath) === '.xlsx' ||
            path.extname(filePath) === '.xls' ||
            path.extname(filePath) === '.csv'
        ) {
            logger.info('calling handle melle file change');
            if (global.isMelleUploading) {
                logger.info('melle is uploading, skipping file change');
                return;
            }
            try {
                global.isMelleUploading = true;
                await convertMelleExcelToJson(path.normalize(melleDirectoryPath + '/' + filePath));
            } catch (error: any) {
                // console error
            } finally {
                global.isMelleUploading = false;
            }
        }
    } catch (error) {
        logger.error(`error convert melle => ${error} filePath ${filePath}`);
    }
}

// Add event listeners
melleWatcher
    .on('add', handleMelleFileChange)
    .on('change', handleMelleFileChange)
    .on('error', (error) => logger.error(`Watcher error: ${error}`));

/// watcher for stock margin
const stockMarginDirectoryPath = path.join(__dirname, '../public/stock_margin');

// Initialize stock margin watcher
const stockMarginWatcher = chokidar.watch(stockMarginDirectoryPath, {
    persistent: true,
    ignoreInitial: true, // Process existing files on startup
    followSymlinks: true,
    cwd: directoryPath,
    disableGlobbing: true,
    depth: 3,
    atomic: 1000
});

async function handleStockMarginFileChange(filePath) {
    try {
        logger.info(`handleStockMarginFileChange ${filePath}`);
        if (
            path.extname(filePath) === '.xlsx' ||
            path.extname(filePath) === '.xls' ||
            path.extname(filePath) === '.csv'
        ) {
            logger.info('calling handle stock margin file change');
            if (global.isStockMarginUploading) {
                logger.info('stock margin is uploading, skipping file change');
                return;
            }
            try {
                global.isStockMarginUploading = true;
                await convertStockMarginExcelToJson(path.normalize(stockMarginDirectoryPath + '/' + filePath));
            } catch (error: any) {
                // console error
            } finally {
                global.isStockMarginUploading = false;
            }
        }
    } catch (error) {
        logger.error(`error convert stock margin => ${error} filePath ${filePath}`);
    }
}

// Add event listeners
stockMarginWatcher
    .on('add', handleStockMarginFileChange)
    .on('change', handleStockMarginFileChange)
    .on('error', (error) => logger.error(`Watcher error: ${error}`));

///

// FTP
const ftpServer = new FTPSrv({ url: `${process.env.FTP_SERVER_URL}:${process.env.FTP_PORT}`, anonymous: true });

ftpServer.on('login', async ({ connection, username, password }, resolve, reject) => {
    try {
        logger.info(`login ${username} and ${password}`);

        const vendorObj = await models.vendors.findOne({
            where: { ftp_username: username }
        });
        logger.info(`vendorObj ${JSON.stringify(vendorObj)}`);

        if (vendorObj) {
            const match = true; // await bcrypt.compare(password, vendorObj.ftp_password);
            logger.info(`match ${match}`);

            if (match) {
                logger.info(`vendor id ${vendorObj.id}`);

                return resolve({ root: `public/stocks/clients/${vendorObj.id}` });
            }
        } else {
            logger.info(`admin ${process.env.ADMIN_FTP_USERNAME} and ${process.env.ADMIN_FTP_PASS}`);

            if (username === process.env.ADMIN_FTP_USERNAME && password === process.env.ADMIN_FTP_PASS) {
                return resolve({ root: `public/stocks/admin` });
            }
        }
        return reject(new Error('Invalid username or password'));
    } catch (error) {
        logger.error(`error ftpServer.on ${error}`);
    }
});

ftpServer
    .listen()
    .then(() => {
        logger.info(`FTPs server is running on ${process.env.FTP_SERVER_URL}:${process.env.FTP_PORT}`);
    })
    .catch((err) => {
        logger.error(`Error starting FTP server: ${err}`);
    });

export = app;
