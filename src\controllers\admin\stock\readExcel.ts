import xlsxToJson from 'xlsx-to-json-lc';
import xlsToJson from 'xls-to-json-lc';
import { getStockModalFields } from './getStockModelFields';
import models from '../../../models';
import { Op } from 'sequelize';
import { logger } from '../../../utils/logger';
import axios from 'axios';
import dotenv from 'dotenv';
import path from 'path';
import csv from 'csvtojson';
import { parse } from 'json2csv';
import fs from 'fs';
import { createChunkArray } from '../../ProductCron';
dotenv.config({ path: path.join(__dirname, '../../../../.env') });

export async function convertExcelCsvToJsonAndSaveCsv(
    filePath: string,
    adminId: string | null,
    vendorId: string | null
) {
    logger.info(`!!!!!filePath convertExcelCsvToJsonAndSaveCsv!!!!!!!! ${filePath}`);

    const saveCsvFile = async (jsonData: any, fileName: string) => {
        logger.info(`!!!!!!!!!!saveCsvFile function started!!!!!!!!!!!!`);
        const filePathToSaveCsv = path.join(__dirname, `../${fileName}`);
        logger.info(`!!!!!!!!!!!Saving Path!!!!!!!!!!!! ${filePathToSaveCsv}`);

        try {
            const csvData = parse(jsonData);
            fs.writeFileSync(filePathToSaveCsv, csvData, 'utf8');
            logger.info(`CSV file saved at ${filePathToSaveCsv}`);

            // const newPath = `/www/wwwroot/projects/diamond_company_api/public/stock/${fileName}
            const newPath = `${process.env.FTP_FILE_RENAME_PATH}/${fileName}`;
            logger.info(`!!!!!!!!newPath!!!!!!!!! ${newPath}`);

            await new Promise((resolve, reject) => {
                fs.rename(filePathToSaveCsv, newPath, (error: any) => {
                    if (error) {
                        logger.error(`!!!!Error Renaming!!!!!!!!! ${error}`);
                        reject(error);
                    }
                    logger.info('SuccessFully Renamed');
                    resolve(true);
                });
            });

            return newPath;
        } catch (error) {
            logger.error('Error saving CSV file', error);
            throw error;
        }
    };

    const isExcel = filePath.split('.')[filePath.split('.').length - 1] !== 'csv';
    logger.info(`!!!!!!!!IsExcel!!!!!!!!!! ${isExcel}`);

    if (isExcel) {
        try {
            return await new Promise((resolve, reject) => {
                let excelToJson: any;

                if (filePath.split('.')[filePath.split('.').length - 1] === 'xlsx') {
                    excelToJson = xlsxToJson;
                } else if (filePath.split('.')[filePath.split('.').length - 1] === 'xls') {
                    excelToJson = xlsToJson;
                }
                return excelToJson(
                    {
                        input: filePath,
                        output: null,
                        lowerCaseHeaders: true
                    },
                    async (error: any, result: any) => {
                        if (error) {
                            reject(error);
                        }

                        logger.info(`!!!!!Result Length!!!!!!!!!!! ${result.length}`);
                        const savedCsvPath = saveCsvFile(result, `converted_file${Date.now()}.csv`);

                        try {
                            await models.cron_logs.create({
                                vendor_id: vendorId,
                                admin_id: adminId,
                                is_successful: true,
                                data: `!!!!!!!!!Stock Data Length!!!!!!!! ${result?.length}`,
                                message: 'CSV Converted',
                                type: 'read file'
                            });
                        } catch (err) {
                            logger.error('err in cron_logs excelToJson creation ', err);
                            logger.error(`vendorId ${vendorId} admin id ${adminId}`);
                        }

                        logger.info('Resolving');
                        resolve(savedCsvPath);
                    }
                );
            });
        } catch (error) {
            logger.error('error in read excel file', error);
            // try {
            //     await models.cron_logs.create({
            //         vendor_id: vendorId,
            //         admin_id: adminId,
            //         is_successful: false,
            //         data: 'error in read xlsx file',
            //         message: JSON.stringify(error),
            //         type: 'read file'
            //     });
            // } catch (err) {
            //     logger.error('error in read xlx file creating log', err);
            // }
        }
    } else {
        try {
            const csvResult = await csv().fromFile(filePath);

            const savedCsvPath = saveCsvFile(csvResult, `converted_file${Date.now()}.csv`);

            try {
                await models.cron_logs.create({
                    vendor_id: vendorId,
                    admin_id: adminId,
                    is_successful: true,
                    data: `!!!!!!!!!Stock Data Length!!!!!!!! ${csvResult?.length}`,
                    message: 'CSV Converted',
                    type: 'read file'
                });
            } catch (error) {
                logger.error('error in create log in read csv file', error);
            }

            return savedCsvPath;
        } catch (error) {
            logger.error('error in read csv file', error);
            // try {
            //     await models.cron_logs.create({
            //         vendor_id: vendorId,
            //         admin_id: adminId,
            //         is_successful: false,
            //         data: 'error in read csv file',
            //         message: JSON.stringify(error),
            //         type: 'read file'
            //     });
            // } catch (err) {
            //     logger.error('error in read csv file creating log', err);
            // }
        }
    }
}

export async function convertExcelToJson(
    filePath: string,
    stockData: any,
    stockIds: string[],
    offset: number,
    adminId: string | null,
    vendorId: string | null,
    vendorMargin: number
) {
    logger.info(`!!!!!filePath!!!!!!!! ${filePath}`);

    if (filePath.split('.')[filePath.split('.').length - 1] !== 'csv') {
        try {
            return await new Promise((resolve, reject) => {
                let excelToJson: any;
                // const newStockIds = [...stockIds];
                // const newStockData: any[] = stockData
                //     .filter((stock) => newStockIds.includes(stock.stock_id))
                //     .map((stock: any) => {
                //         if (newStockIds.includes(stock.stock_id)) {
                //             return {
                //                 stock_id: stock.stock_id,
                //                 certificate_number: stock.certificate_number,
                //                 status: stock.status,
                //                 id: stock.id
                //             };
                //         }
                //     });

                if (filePath.split('.')[filePath.split('.').length - 1] === 'xlsx') {
                    excelToJson = xlsxToJson;
                } else if (filePath.split('.')[filePath.split('.').length - 1] === 'xls') {
                    excelToJson = xlsToJson;
                }
                let apiRes;
                const cronErr: string[] = [];
                let cronSuccessful = true;
                return excelToJson(
                    {
                        input: filePath,
                        output: null,
                        lowerCaseHeaders: true
                    },
                    async (error: any, result: any) => {
                        if (error) {
                            reject(error);
                        }

                        const stockList: any = [];

                        const chunkedData = createChunkArray(result);
                        logger.info('Started for chunks');
                        let chunkIndex = 1;
                        for (const chunkItem of chunkedData) {
                            logger.info(
                                `!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!index!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ${chunkIndex}`
                            );
                            try {
                                const payload = {
                                    stock_array_object: chunkItem,
                                    vendor_id: vendorId?.toString(),
                                    admin_id: adminId?.toString()
                                };
                                const addStockConfig = {
                                    headers: {
                                        authorization: process.env.ADD_STOCK_AUTH
                                    }
                                };
                                apiRes = await axios.post(process.env.ADD_STOCK_URL ?? '', payload, addStockConfig);
                                logger.info(
                                    `api excel ${JSON.stringify(apiRes.data)} for admin ${adminId} --vendor ${vendorId}`
                                );
                            } catch (error: any) {
                                logger.error('error in ADD_STOCK_URL xls', error);
                                cronErr.push(JSON.stringify(error?.response?.data ?? error));
                                cronSuccessful = false;
                            } finally {
                                chunkIndex++;
                            }
                        }
                        logger.info('Ended for chunks');
                        try {
                            await models.cron_logs.create({
                                vendor_id: vendorId,
                                admin_id: adminId,
                                is_successful: cronSuccessful,
                                data: JSON.stringify(result),
                                message: cronSuccessful ? JSON.stringify(apiRes.data) : JSON.stringify(cronErr),
                                type: 'read file'
                            });
                        } catch (err) {
                            logger.error('err in cron_logs excelToJson creation ', err);
                            logger.error(`vendorId ${vendorId} admin id ${adminId}`);
                        }

                        // for (const item of result) {
                        //     const stockCreateObject: any = await getStockModalFields(
                        //         item,
                        //         offset,
                        //         null,
                        //         vendorId,
                        //         vendorMargin
                        //     );
                        //     if (stockCreateObject) {
                        //         const foundedStockData: any = newStockData.find((stockItem: any) => {
                        //             /// same stock ids
                        //             if (stockItem.stock_id === stockCreateObject.stock_id) {
                        //                 return stockItem;
                        //             }

                        //             /// same certificate number
                        //             if (
                        //                 stockCreateObject.certificate_number &&
                        //                 stockItem.certificate_number &&
                        //                 stockCreateObject.certificate_number === stockItem.certificate_number
                        //             ) {
                        //                 return stockItem;
                        //             }

                        //             return;
                        //         });
                        //         if (foundedStockData) {
                        //             // update query
                        //             if (foundedStockData.status === 'AVAILABLE') {
                        //                 await models.stocks.update(stockCreateObject, {
                        //                     where: {
                        //                         id: foundedStockData.id
                        //                     }
                        //                 });
                        //             }
                        //         } else {
                        //             stockList.push(stockCreateObject);
                        //             newStockIds.push(stockCreateObject.stock_id);
                        //             newStockData.push({
                        //                 stock_id: stockCreateObject.stock_id,
                        //                 certificate_number: stockCreateObject.certificate_number
                        //             });
                        //         }
                        //     }
                        // }
                        logger.info('Resolving');
                        resolve(stockList);
                    }
                );
            });
        } catch (error) {
            logger.error('error in read csv file', error);
            try {
                await models.cron_logs.create({
                    vendor_id: vendorId,
                    admin_id: adminId,
                    is_successful: false,
                    data: 'error in read xlsx file',
                    message: JSON.stringify(error),
                    type: 'read file'
                });
            } catch (err) {
                logger.error('error in read xlx file creating log', err);
            }
        }
    } else {
        try {
            const csvResult = await csv().fromFile(filePath);
            const chunkedData = createChunkArray(csvResult);
            let res;
            const csvError: string[] = [];
            let cronSuccessful = true;
            logger.info('Started for chunks');
            let chunkIndex = 1;
            for (const item of chunkedData) {
                logger.info(
                    `!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!index!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ${chunkIndex}`
                );
                try {
                    // logger.info('item', item);
                    const addStockPayload = {
                        stock_array_object: item,
                        vendor_id: vendorId?.toString(),
                        admin_id: adminId?.toString()
                    };
                    const addCsvStockConfig = {
                        headers: {
                            authorization: process.env.ADD_STOCK_AUTH
                        }
                    };

                    res = await axios.post(process.env.ADD_STOCK_URL ?? '', addStockPayload, addCsvStockConfig);
                    logger.info(`api csv ${JSON.stringify(res.data)} for admin ${adminId} --vendor ${vendorId}`);
                } catch (error: any) {
                    logger.error('error in ADD_STOCK_URL read admin csv file', error);
                    csvError.push(JSON.stringify(error?.response?.data ?? error));
                    cronSuccessful = false;
                } finally {
                    chunkIndex++;
                }
            }
            logger.info('Ended for chunks');
            try {
                await models.cron_logs.create({
                    vendor_id: vendorId,
                    admin_id: adminId,
                    is_successful: cronSuccessful,
                    data: JSON.stringify(csvResult),
                    message: cronSuccessful ? JSON.stringify(res.data) : JSON.stringify(csvError),
                    type: 'read file'
                });
            } catch (error) {
                logger.error('error in create log in read csv file', error);
            }
            return true;
        } catch (error) {
            logger.error('error in read csv file', error);
            try {
                await models.cron_logs.create({
                    vendor_id: vendorId,
                    admin_id: adminId,
                    is_successful: false,
                    data: 'error in read csv file',
                    message: JSON.stringify(error),
                    type: 'read file'
                });
            } catch (err) {
                logger.error('error in read csv file creating log', err);
            }
        }
    }
}
