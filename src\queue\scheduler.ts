import { Queue } from 'bullmq';
import { redisConnection } from './../common/redisConnection';
import { TRIGGER_QUEUE_NAME } from './../common/queueNames';
import { logger } from '../utils/logger';

const triggerQueue = new Queue(TRIGGER_QUEUE_NAME, {
    connection: redisConnection
});

async function scheduleInventoryTrigger() {
    // Remove any existing repeatable job with the same ID to avoid duplicates
    await triggerQueue.removeJobScheduler(
        `trigger-inventory-fetch:::*/30 * * * *` // Key matches the pattern below
    );

    // Add the repeatable job
    await triggerQueue.add(
        'trigger-inventory-fetch', // Job name
        {}, // Job data (empty for trigger)
        {
            repeat: {
                pattern: '*/30 * * * *', // Cron pattern for every 30 minutes
                // immediately: true // Uncomment to run immediately on start for testing
            },
            jobId: 'repeatable-inventory-trigger', // Optional: Assign a specific ID
            removeOnComplete: true, // Keep queue clean
            removeOnFail: 100 // Keep failed trigger jobs for inspection
        }
    );

    logger.info('Inventory fetch trigger job scheduled to run every 30 minutes.');
}

scheduleInventoryTrigger().catch((err) => {
    logger.error('Failed to schedule trigger job:', err);
    process.exit(1);
});

// Keep the scheduler running (or manage via PM2/systemd)
logger.info('Scheduler process started. Waiting for next schedule...');
// Note: In a real app, you might want more robust lifecycle management.
