import getStockFields from './getStockFields';
import { getStockFieldsAliases } from '../../../utils/stockModelFieldsConstant';
export const getStockModalFields = (item, offset, clientName, vendorId, vendorMargin) => {
    // eslint-disable-next-line no-useless-catch
    try {
        // if stock Id is there then insert
        const stockId = getStockFields.getStringValue(item, getStockFieldsAliases.stockId);

        if (stockId) {
            const discount = getStockFields.getDoubleValue(item, getStockFieldsAliases.discount);
            const finalDiscount =
                offset && offset > 0 ? parseFloat(String(discount)) - parseFloat(offset) : parseFloat(String(discount));
            const pricePerCaret = getStockFields.getDoubleValue(item, getStockFieldsAliases.pricePerCaret);
            const weight = getStockFields.getDoubleValue(item, getStockFieldsAliases.weight);
            let offsetPricePerCaret = pricePerCaret + (pricePerCaret * offset) / 100;
            const finalPriceOrigin = getStockFields.getFinalPrice(weight, offsetPricePerCaret);
            if (vendorMargin) {
                offsetPricePerCaret = offsetPricePerCaret + (offsetPricePerCaret * vendorMargin) / 100;
            }
            const finalPrice = getStockFields.getFinalPrice(weight, offsetPricePerCaret);

            const createStockObject = {
                client_name: clientName,
                stock_id: getStockFields.getStringValue(item, getStockFieldsAliases.stockId),
                rapnet_lot_id: getStockFields.getStringValue(item, getStockFieldsAliases.rapnet_lot_id),
                availability: getStockFields.getStringValue(item, getStockFieldsAliases.availability),
                shape: getStockFields.getStringValue(item, getStockFieldsAliases.shape),
                weight,
                color: getStockFields.getStringValue(item, getStockFieldsAliases.color),
                clarity: getStockFields.getStringValue(item, getStockFieldsAliases.clarity),
                cut: getStockFields.getStringValue(item, getStockFieldsAliases.cut),
                polish: getStockFields.getStringValue(item, getStockFieldsAliases.polish),
                symmetry: getStockFields.getStringValue(item, getStockFieldsAliases.symmetry),
                fluorescence_intensity:
                    getStockFields.getObjectValue(item, getStockFieldsAliases.fluorescence_intensity) &&
                    getStockFields.getObjectValue(item, getStockFieldsAliases.fluorescence_intensity).Intesity,
                fluorescence_color: getStockFields.getStringValue(item, getStockFieldsAliases.fluorescence_color),
                measurements: getStockFields.getStringValue(item, getStockFieldsAliases.measurements),
                shade: getStockFields.getStringValue(item, getStockFieldsAliases.shade),
                milky: getStockFields.getStringValue(item, getStockFieldsAliases.milky),
                eye_clean: getStockFields.getStringValue(item, getStockFieldsAliases.eye_clean),
                lab: getStockFields.getStringValue(item, getStockFieldsAliases.lab),
                certificate_number: getStockFields.getStringValue(item, getStockFieldsAliases.certificate_number),
                location: '',
                // treatment: getStockFields.getStringValue(
                //   item,
                //   "Enhance, Enhancement, Enhancements, Enhance-ments, Treatments, treatments"
                // ),
                treatment: 'Lab Grown',
                discounts: finalDiscount,
                price_per_caret: getStockFields.getDoubleValue(item, getStockFieldsAliases.price_per_caret),
                /**
                 * Calculation
                 * weight * pricePerCaret
                 * This is the final discounted amount
                 */
                final_price: finalPrice,
                final_price_ori: finalPriceOrigin,
                meas_depth: getStockFields.getDoubleValue(item, getStockFieldsAliases.meas_depth),
                // pavilion_depth: getStockFields.getDoubleValue(
                //   item,
                //   "Pavil, Pavilion, Pavilion Percent, PavilionDepth, PavilionPct"
                // ),
                table: getStockFields.getDoubleValue(item, getStockFieldsAliases.table),
                girdle_thin: getStockFields.getStringValue(item, getStockFieldsAliases.girdle_thin),
                girdle_thick: getStockFields.getStringValue(item, getStockFieldsAliases.girdle_thick),
                girdle_per: getStockFields.getStringValue(item, getStockFieldsAliases.girdle_per),
                girdle_condition: getStockFields.getStringValue(item, getStockFieldsAliases.girdle_condition),
                culet_size: getStockFields.getStringValue(item, getStockFieldsAliases.culet_size),
                culet_condition: getStockFields.getStringValue(item, getStockFieldsAliases.culet_condition),
                crown_height: getStockFields.getDoubleValue(item, getStockFieldsAliases.crown_height),
                crown_angle: getStockFields.getDoubleValue(item, getStockFieldsAliases.crown_angle),
                pavilion_depth: getStockFields.getDoubleValue(item, getStockFieldsAliases.pavilion_depth),
                pavilion_angle: getStockFields.getDoubleValue(item, getStockFieldsAliases.pavilion_angle),
                inscription: getStockFields.getStringValue(item, getStockFieldsAliases.inscription),
                certificate_comment: getStockFields.getStringValue(item, getStockFieldsAliases.certificate_comment),
                certfile: getStockFields.getStringValue(item, getStockFieldsAliases.certfile, true),
                keytosymbols: getStockFields.getStringValue(item, getStockFieldsAliases.keytosymbols),
                white_inclusion: getStockFields.getStringValue(item, getStockFieldsAliases.white_inclusion),
                black_inclusion: getStockFields.getStringValue(item, getStockFieldsAliases.black_inclusion),
                open_inclusion: getStockFields.getStringValue(item, getStockFieldsAliases.open_inclusion),
                fancy_color: getStockFields.getStringValue(item, getStockFieldsAliases.fancy_color),
                fancy_color_intensity: getStockFields.getStringValue(item, getStockFieldsAliases.fancy_color_intensity),
                fancy_color_overtone: getStockFields.getStringValue(item, getStockFieldsAliases.fancy_color_overtone),
                country: getStockFields.getStringValue(item, getStockFieldsAliases.country),
                state: getStockFields.getStringValue(item, getStockFieldsAliases.state),
                city: getStockFields.getStringValue(item, getStockFieldsAliases.city),
                diamond_video: getStockFields.getStringValue(item, getStockFieldsAliases.diamond_video, true),
                diamond_image: getStockFields.getStringValue(item, getStockFieldsAliases.diamond_image, true),
                diamond_image_2: getStockFields.getStringValue(item, getStockFieldsAliases.diamond_image_2),
                hearts_image: getStockFields.getStringValue(item, getStockFieldsAliases.hearts_image),
                arrows_image: getStockFields.getStringValue(item, getStockFieldsAliases.arrows_image),
                aset_image: getStockFields.getStringValue(item, getStockFieldsAliases.aset_image),
                idealscope_image: getStockFields.getStringValue(item, getStockFieldsAliases.idealscope_image),
                diamond_type: getStockFields.getStringValue(item, getStockFieldsAliases.diamond_type)
                    ? getStockFields.getStringValue(item, getStockFieldsAliases.diamond_type)
                    : 'CVD',
                lab_grown_type: getStockFields.getStringValue(item, getStockFieldsAliases.lab_grown_type)
                    ? getStockFields.getStringValue(item, getStockFieldsAliases.lab_grown_type)
                    : 'CVD',
                // lab_grown_type: 'CVD',
                growth_type: getStockFields.getStringValue(item, getStockFieldsAliases.growth_type)
                    ? getStockFields.getStringValue(item, getStockFieldsAliases.growth_type)
                    : 'CVD',
                // calculation (Old One) (Can Ignore)
                // weight * pricePerCaret
                // rap_per_caret: getStockFields.getRapPerCaretPrice(
                //   getStockFields.getDoubleValue(
                //     item,
                //     "Carat, CaratSize, CaratWeight, Ct, CtSize, CtWeight, Weight, Sz, Weight, weight, carat"
                //   ),
                //   getStockFields.getDoubleValue(
                //     item,
                //     "$/ct, RapnetAskingPrice, AskingPrice, PerCarat, PerCt, Prc, Price, PriceCarat, PriceCt, PricePerCarat, pricepercarat, PricePerCt, Px"
                //   )
                // ),
                /**
                 * Rap Price from excel
                 */
                rap_per_caret: getStockFields.getDoubleValue(item, getStockFieldsAliases.rap_per_caret),
                // Old One (Can Ignore)
                // rap_price: getStockFields.getDoubleValue(item, "rap price"),
                /**
                 * Rap price is final price without discount
                 * Weight * RapPerCaret
                 */
                rap_price: getStockFields.getRapPerCaretPrice(
                    getStockFields.getDoubleValue(item, getStockFieldsAliases.weight),
                    getStockFields.getDoubleValue(item, getStockFieldsAliases.rap_price)
                ),
                lw_ratio: getStockFields.getDoubleValue(item, getStockFieldsAliases.lw_ratio),
                brand: getStockFields.getStringValue(item, getStockFieldsAliases.brand),
                status:
                    getStockFields.getStringValue(item, getStockFieldsAliases.status) === 'STOCK'
                        ? 'AVAILABLE'
                        : getStockFields.getStringValue(item, getStockFieldsAliases.status) === 'MEMO'
                        ? 'MEMO'
                        : getStockFields.getStringValue(item, getStockFieldsAliases.status) === 'HOLD'
                        ? 'HOLD'
                        : 'AVAILABLE',
                is_lab_grown: getStockFields.getStringValue(item, getStockFieldsAliases.is_lab_grown)
                    ? getStockFields.getStringValue(item, getStockFieldsAliases.is_lab_grown) === 'lab_grown'
                        ? true
                        : false
                    : false,
                is_active: true,
                _deleted: false
            };

            return createStockObject;
        }

        return null;
    } catch (e) {
        throw e;
    }
};
