import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface WishlistStockAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    stock_id: number; // days
    updatedAt?: Date;
}

interface WishlistStockCreationAttributes extends Optional<WishlistStockAttributes, 'id'> {}

interface WishlistStockInstance
    extends Model<WishlistStockAttributes, WishlistStockCreationAttributes>,
        WishlistStockAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type WishlistStockStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => WishlistStockInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const wishlistStock = sequelize.define<WishlistStockInstance>(
        'wishlist_stocks',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.UUID,
                allowNull: false
            },
            stock_id: {
                type: DataTypes.UUID,
                allowNull: false
            }
        },
        {
            freezeTableName: true
        }
    ) as WishlistStockStatic;

    wishlistStock.associate = (models) => {
        wishlistStock.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
        wishlistStock.belongsTo(models.stocks, {
            foreignKey: 'stock_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };

    //
    // await wishlistStock.sync({alter: true});

    return wishlistStock;
};
