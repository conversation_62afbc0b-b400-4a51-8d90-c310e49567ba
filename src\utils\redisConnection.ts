import Queue from 'bull';
import { logger } from './logger';
import * as bluebird from 'bluebird';
import dotenv from 'dotenv';
dotenv.config();

export class RedisConnection {
    static client: any;
    // Initialize your redis connection
    public static init(): any {
        const { REDIS_URL, REDIS_PORT, REDIS_PASS } = process.env;

        logger.info(`Redis URL: ${REDIS_URL}`);
        logger.info(`Redis Port: ${REDIS_PORT}`);
        logger.info(`Redis Password: ${REDIS_PASS}`);

        // @ts-ignore
        this.client = new Queue({
            host: '*************',
            port: 6379,
            password: 'va1EmxsNF0iQtL8hy+LOcqn2/iSaa34JqJauKcUU/foKeAdaahzxJQ1qG3W+QZDAlUZqNWs7+03Fk/eQ192'
        });

        (this.client as any).Promise = bluebird;
        this.client.on('connect', () => logger.info('Connected to redis server'));
        this.client.on('error', () => logger.info('Failed to connect to redis server'));
    }
}
