{"name": "diamond", "version": "1.0.0", "description": "Starter Kit for NodeJs", "main": "dist/index.js", "scripts": {"start:scheduler": "node dist/queue/scheduler.js", "start:job-creator": "node dist/queue/jobCreator.js", "start:worker": "node dist/queue/worker.js", "start:dashboard": "node dist/queue/dashboard.js", "clean": "rimraf dist/", "lint": "tslint --fix -c  tslint.json \"src/**/**.ts\"", "tsc": "tsc --project ./tsconfig.json", "prettier-all": "npx prettier --write \"src/**/*.+(ts|tsx|js|css|json)\"", "precompile": "npm run prettier-all && npm run lint && npm run clean", "compile": "npm run tsc", "build": "npm run compile", "postbuild": "rimraf --glob \"dist/**/spec\" \"dist/**/*.spec.js\" && cpx apidoc.json dist/apidoc.json && apidoc -i dist/controllers -o dist/public/apidoc", "watch": "tsc -w -p ./src -p ./tsconfig.json", "dev": "nodemon ./src/index.ts", "dev:debug": "export DEBUG_PORT=9229 && nodemon ./src/index.ts", "start": "node ./dist/index.js", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js -f src/typeorm/config/ormconfig.ts", "tslint-check": "tslint-config-prettier-check ./tslint.json"}, "prettier": {"trailingComma": "none", "singleQuote": true, "printWidth": 120, "tabWidth": 4, "semi": true}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.4.9", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "cpx": "^1.5.0", "eslint": "^8.50.0", "nodemon": "^3.0.1", "prettier": "^2.8.8", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "tslint-clean-code": "^0.2.10", "tslint-config-prettier": "^1.18.0", "typescript": "^5.1.6"}, "keywords": ["Shopify", "API", "Client"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@bull-board/api": "^6.9.2", "@bull-board/express": "^6.9.2", "@bull-board/ui": "^6.9.2", "@sentry/node": "^7.62.0", "apidoc": "^1.1.0", "async": "^3.2.5", "axios": "^1.7.2", "bcrypt": "^5.1.1", "bluebird": "^3.7.2", "bull": "^4.11.2", "bullmq": "^5.52.0", "child_process": "^1.0.2", "chokidar": "^3.6.0", "cluster": "^0.7.7", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "discord-webhook-node": "^1.1.8", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.1", "ftp-srv": "^4.6.3", "ioredis": "^5.6.1", "json2csv": "^6.0.0-alpha.2", "json2xls": "^0.1.2", "jsonwebtoken": "^9.0.1", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "pg": "^8.11.2", "reflect-metadata": "^0.1.13", "sequelize": "^6.32.1", "winston": "^3.10.0", "winston-transport": "^4.5.0", "xls-to-json-lc": "^0.3.4", "xlsx-to-json-lc": "^0.5.0"}}