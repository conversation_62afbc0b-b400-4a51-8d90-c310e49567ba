import { BuildOptions, Model, Optional, Sequelize, UUIDV4 } from 'sequelize';

export interface BannerAttributes {
    id?: string; // id is an auto-generated UUID
    order: number;
    name?: string;
    description?: string;
    banner_image?: string;
    banner_image_url?: string;
    position?: string;
    deep_link?: string;
    type?: string;
    payload?: string;
    is_active?: boolean;
    _deleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    // Association Fields
}

interface BannerCreationAttributes extends Optional<BannerAttributes, 'id'> {}

interface BannerInstance extends Model<BannerAttributes, BannerCreationAttributes>, BannerAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type BannerStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => BannerInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const banners = sequelize.define<BannerInstance>(
        'banners',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            order: {
                type: DataTypes.INTEGER,
                allowNull: true,
                defaultValue: 0
            },
            name: {
                type: DataTypes.STRING,
                allowNull: true
            },
            description: {
                type: DataTypes.STRING,
                allowNull: true
            },
            banner_image: {
                type: DataTypes.STRING,
                allowNull: true
            },
            banner_image_url: {
                type: DataTypes.STRING,
                allowNull: true
            },
            position: {
                type: DataTypes.STRING,
                values: ['top', 'bottom', 'drawer', 'dashboard'],
                allowNull: true
            },
            deep_link: {
                type: DataTypes.STRING,
                allowNull: true
            },
            type: {
                type: DataTypes.STRING,
                values: ['stock', 'other']
            },
            payload: {
                type: DataTypes.TEXT
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                allowNull: true,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as BannerStatic;

    // TODO: make common function to sync
    // await banners.sync({ alter: true });

    return banners;
};
