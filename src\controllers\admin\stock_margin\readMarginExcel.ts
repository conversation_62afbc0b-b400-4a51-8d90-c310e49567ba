import xlsxToJson from 'xlsx-to-json-lc';
import xlsToJson from 'xls-to-json-lc';
import models from '../../../models';
import { logger } from '../../../utils/logger';
import axios from 'axios';
import dotenv from 'dotenv';
import path from 'path';
import csv from 'csvtojson';
import { createChunkArray } from '../../ProductCron';
dotenv.config({ path: path.join(__dirname, '../../../../.env') });

export async function convertStockMarginExcelToJson(filePath: string) {
    try {
        logger.info(`!!!!!convertStockMarginExcelToJson function filePath!!!!!!!! ${filePath}`);
        let resultData;
        if (filePath.split('.')[filePath.split('.').length - 1] !== 'csv') {
            try {
                resultData = await new Promise(async (resolve, reject) => {
                    let excelToJson: any;

                    if (filePath.split('.')[filePath.split('.').length - 1] === 'xlsx') {
                        excelToJson = xlsxToJson;
                    } else if (filePath.split('.')[filePath.split('.').length - 1] === 'xls') {
                        excelToJson = xlsToJson;
                    }
                    return excelToJson(
                        {
                            input: filePath,
                            output: null,
                            lowerCaseHeaders: true
                        },
                        async (error: any, result: any) => {
                            if (error) {
                                reject(error);
                            }

                            resolve(result);
                        }
                    );
                });
            } catch (error) {
                resultData = null;
            }
        } else {
            try {
                const csvResult = await csv().fromFile(filePath);
                resultData = csvResult;
            } catch (error) {
                resultData = null;
            }
        }

        /// reformat excel data json to model specific
        resultData = reformatStockMarginData(resultData);

        const chunkedData = createChunkArray(resultData);

        // destroy all old margin data
        const deleteStockMarginConfig = {
            headers: {
                authorization: process.env.ADD_STOCK_AUTH
            }
        };

        const resOfDeleteStockMarginData = await axios.delete(
            process.env.STOCK_MARGIN_URL ?? '',
            deleteStockMarginConfig
        );

        let res;
        const errorArray: string[] = [];
        let cronSuccessful = true;
        logger.info('Started for chunks');
        let chunkIndex = 1;
        for (const item of chunkedData) {
            logger.info(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!index!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ${chunkIndex}`);
            if (chunkIndex === 1) {
                logger.info(`First chunk ${JSON.stringify(item, null, 2)}`);
            }
            try {
                const addStockMarginPayload = {
                    stock_margin_array: item
                };
                const addStockMarginConfig = {
                    headers: {
                        authorization: process.env.ADD_STOCK_AUTH
                    }
                };

                res = await axios.post(process.env.STOCK_MARGIN_URL ?? '', addStockMarginPayload, addStockMarginConfig);
                logger.info(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!Success for index!!!!!!!!!!!!!!!!!!!!! ${chunkIndex}`);
            } catch (error: any) {
                logger.error(`error in ADD_MELLE_URL read melle file for index ::::: ${chunkIndex} Error: ${error}`);
                errorArray.push(JSON.stringify(error?.response?.data ?? error));
                cronSuccessful = false;
            } finally {
                chunkIndex++;
            }
        }
        try {
            await models.cron_logs.create({
                is_successful: cronSuccessful,
                data: JSON.stringify(resultData),
                message: cronSuccessful ? JSON.stringify(res?.data) : JSON.stringify(errorArray),
                type: 'read file'
            });
        } catch (err) {
            logger.error('err in cron_logs for add margin creation from excel file ', err);
        }
        return true;
    } catch (error: any) {
        logger.error('error in read excel file for  add margin creation', error);
        try {
            await models.cron_logs.create({
                is_successful: false,
                data: 'error in read stock margin file',
                message: JSON.stringify(error),
                type: 'read stock margin file'
            });
        } catch (err) {
            logger.error('error in read xlx file creating log', err);
        }
        return false;
    }
}

/// reformat stock margin data
function reformatStockMarginData(excelData, config = {}) {
    // Internal utility functions
    const formatWeight = (weight, separators, replaceSeparator) => {
        for (const separator of separators) {
            if (weight.includes(separator)) {
                return weight.replace(separator, replaceSeparator);
            }
        }
        return weight;
    };

    const parseMarginValue = (marginStr) => {
        return parseFloat(marginStr.split('%')[0]);
    };

    // Default configuration
    const defaultConfig = {
        clarityGrades: ['vvs1', 'vvs2', 'vs1', 'vs2', 'si1'],
        defaultShape: 'Fancy',
        defaultColor: 'D,E,F,G,H,I,J',
        weightSeparators: [' to ', '-', '*', '#', '=>', '~', '|', '/'],
        weightReplaceSeparator: '-'
    };

    // Merge configurations
    const finalConfig = { ...defaultConfig, ...config };

    // Process data
    return excelData.reduce((margins, row) => {
        const weightFormatted = formatWeight(
            row.weigth,
            finalConfig.weightSeparators,
            finalConfig.weightReplaceSeparator
        );

        const newMargins = finalConfig.clarityGrades
            .filter((clarity) => row[clarity])
            .map((clarity) => ({
                shape: row.shape || finalConfig.defaultShape,
                weight: weightFormatted,
                color: row.color || finalConfig.defaultColor,
                clarity,
                margin: parseMarginValue(row[clarity])
            }));

        return [...margins, ...newMargins];
    }, []);
}
