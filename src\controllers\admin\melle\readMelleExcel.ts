import xlsxToJson from 'xlsx-to-json-lc';
import xlsToJson from 'xls-to-json-lc';
import models from '../../../models';
import { Op } from 'sequelize';
import { logger } from '../../../utils/logger';
import axios from 'axios';
import dotenv from 'dotenv';
import path from 'path';
import csv from 'csvtojson';
import { createChunkArray } from '../../ProductCron';
dotenv.config({ path: path.join(__dirname, '../../../../.env') });

export async function convertMelleExcelToJson(filePath: string) {
    try {
        logger.info(`!!!!!convertMelleExcelToJson function filePath!!!!!!!! ${filePath}`);
        let resultData;
        if (filePath.split('.')[filePath.split('.').length - 1] !== 'csv') {
            try {
                resultData = await new Promise(async (resolve, reject) => {
                    let excelToJson: any;

                    if (filePath.split('.')[filePath.split('.').length - 1] === 'xlsx') {
                        excelToJson = xlsxToJson;
                    } else if (filePath.split('.')[filePath.split('.').length - 1] === 'xls') {
                        excelToJson = xlsToJson;
                    }
                    return excelToJson(
                        {
                            input: filePath,
                            output: null,
                            lowerCaseHeaders: true
                        },
                        async (error: any, result: any) => {
                            if (error) {
                                reject(error);
                            }

                            resolve(result);
                        }
                    );
                });
            } catch (error) {
                resultData = null;
            }
        } else {
            try {
                const csvResult = await csv().fromFile(filePath);
                resultData = csvResult;
            } catch (error) {
                resultData = null;
            }
        }

        const filteredData: any = resultData?.filter((item: any) => item?.price_per_caret);

        const chunkedData = createChunkArray(filteredData);

        // destroy all old melle data
        const deleteMelleConfig = {
            headers: {
                authorization: process.env.ADD_STOCK_AUTH
            }
        };

        const resOfDeleteMelleData = await axios.delete(process.env.DELETE_MELLE_URL ?? '', deleteMelleConfig);

        let res;
        const errorArray: string[] = [];
        let cronSuccessful = true;
        logger.info('Started for chunks');
        let chunkIndex = 1;
        for (const item of chunkedData) {
            logger.info(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!index!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ${chunkIndex}`);
            if (chunkIndex === 1) {
                logger.info(`First chunk ${JSON.stringify(item, null, 2)}`);
            }
            try {
                const addMellePayload = {
                    melle_array: item
                };
                const addMelleConfig = {
                    headers: {
                        authorization: process.env.ADD_STOCK_AUTH
                    }
                };

                res = await axios.post(process.env.ADD_MELLE_URL ?? '', addMellePayload, addMelleConfig);
                logger.info(`!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!Success for index!!!!!!!!!!!!!!!!!!!!! ${chunkIndex}`);
            } catch (error: any) {
                logger.error(`error in ADD_MELLE_URL read melle file for index ::::: ${chunkIndex} Error: ${error}`);
                errorArray.push(JSON.stringify(error?.response?.data ?? error));
                cronSuccessful = false;
            } finally {
                chunkIndex++;
            }
        }
        try {
            await models.cron_logs.create({
                is_successful: cronSuccessful,
                data: JSON.stringify(resultData),
                message: cronSuccessful ? JSON.stringify(res?.data) : JSON.stringify(errorArray),
                type: 'read file'
            });
        } catch (err) {
            logger.error('err in cron_logs for melle creation from excel file ', err);
        }
        return true;
    } catch (error: any) {
        logger.error('error in read excel file for melle creation', error);
        try {
            await models.cron_logs.create({
                is_successful: false,
                data: 'error in read melle file',
                message: JSON.stringify(error),
                type: 'read melle file'
            });
        } catch (err) {
            logger.error('error in read xlx file creating log', err);
        }
        return false;
    }
}
