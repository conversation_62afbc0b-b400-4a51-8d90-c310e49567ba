const http = require('http');

// Test the diagnostic function
function testFTPPermissions() {
    const postData = JSON.stringify({
        vendor_id: "test-vendor-123"
    });

    const options = {
        hostname: 'localhost',
        port: 3007,
        path: '/api/v1/vendor/test-ftp-folder-permissions',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'f9Zx3LwQ7N',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    const req = http.request(options, (res) => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Headers: ${JSON.stringify(res.headers)}`);

        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            console.log('Response Body:');
            console.log(data);

            // Now test the folder creation
            testFolderCreation();
        });
    });

    req.on('error', (e) => {
        console.error(`Problem with request: ${e.message}`);
    });

    req.write(postData);
    req.end();
}

// Test the folder creation function
function testFolderCreation() {
    console.log('\n--- Testing Folder Creation ---');

    const postData = JSON.stringify({});

    const options = {
        hostname: 'localhost',
        port: 3007,
        path: '/api/v1/vendor/create-diamond-type-folder-script',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'f9Zx3LwQ7N',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    const req = http.request(options, (res) => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Headers: ${JSON.stringify(res.headers)}`);

        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            console.log('Response Body:');
            console.log(data);
        });
    });

    req.on('error', (e) => {
        console.error(`Problem with request: ${e.message}`);
    });

    req.write(postData);
    req.end();
}

console.log('Testing FTP Permissions API...');
testFTPPermissions();
