import { Op } from 'sequelize';
import models from '../models';
import { convertExcelCsvToJsonAndSaveCsv, convertExcelToJson } from './admin/stock/readExcel';
import { logger } from '../utils/logger';
import axios from 'axios';
import dotenv from 'dotenv';
import * as fs from 'fs';

// for FTP vendor
export const processFileChange = async (vendorId, adminId, filePath, diamondType) => {
    const filePathOfSavedCsv: any = await convertExcelCsvToJsonAndSaveCsv(filePath, adminId, vendorId);
    logger.info(`!!!!!!!!filePathOfSavedCsv!!!!!!!!!!! ${filePathOfSavedCsv}`);

    if (vendorId) {
        const vendorData = await models.vendors.findOne({
            where: {
                [Op.and]: [
                    { is_blacklisted: false },
                    { is_terms_accepted: true },
                    { is_verified: true },
                    { _deleted: false },
                    { is_active: true },
                    { id: vendorId }
                ]
            }
        });

        if (!vendorData) {
            logger.warn(`!!!!!!!!!Vendor Data Not Found!!!!!!!!!`);
            return true;
        }

        if (vendorData.is_blacklisted) {
            logger.warn(`!!!!!!!!!Vendor is BlackListed!!!!!!!!!`);
            return true;
        }

        if (!vendorData.is_terms_accepted) {
            logger.warn(`!!!!!!!!!Vendor is not accepted the terms!!!!!!!!!`);
            return true;
        }

        if (!vendorData.is_verified) {
            logger.warn(`!!!!!!!!!Vendor is not verified!!!!!!!!!`);
            return true;
        }

        if (!vendorData.is_active) {
            logger.warn(`!!!!!!!!!Vendor is not active!!!!!!!!!`);
            return true;
        }

        if (vendorData._deleted) {
            logger.warn(`!!!!!!!!!Vendor is Deleted!!!!!!!!!`);
            return true;
        }
    }

    if (filePathOfSavedCsv) {
        logger.info(`!!!!!!!!!!Making the call!!!!!!!!!!!`);

        const addStockPayload = {
            vendor_id: vendorId?.toString(),
            filePath: filePathOfSavedCsv,
            admin_id: adminId?.toString(),
            diamond_type: diamondType
        };

        const addCsvStockConfig = {
            headers: {
                authorization: process.env.ADD_STOCK_AUTH
            }
        };

        const res = await axios.post(process.env.ADD_STOCK_URL ?? '', addStockPayload, addCsvStockConfig);
    }

    logger.info('Done');
    return true;
};
