import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface UserNotificationAttributes {
    id: string; // id is an auto-generated UUID
    user_id: string;
    notification_type: string;
    title: string;
    message: string;
    payload: object;
    is_read: boolean;
    _deleted: boolean;
}

interface UserNotificationCreationAttributes extends Optional<UserNotificationAttributes, 'id'> {}

interface UserNotificationInstance
    extends Model<UserNotificationAttributes, UserNotificationCreationAttributes>,
        UserNotificationAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type UserNotificationStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => UserNotificationInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const userNotification = sequelize.define<UserNotificationInstance>(
        'user_notifications',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            user_id: {
                type: DataTypes.STRING,
                allowNull: false
            },
            notification_type: {
                type: DataTypes.STRING,
                allowNull: false
            },
            title: {
                type: DataTypes.STRING,
                allowNull: false
            },
            message: {
                type: DataTypes.STRING,
                allowNull: true
            },
            payload: {
                type: DataTypes.JSONB,
                allowNull: true
            },
            is_read: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            }
        },
        {
            freezeTableName: true
        }
    ) as UserNotificationStatic;

    userNotification.associate = (models) => {
        userNotification.belongsTo(models.users, {
            foreignKey: 'user_id',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });
    };
    //
    // await userNotification.sync({ alter: true })

    return userNotification;
};
