export default {
    apps: [
        {
            name: 'inventory-scheduler',
            script: './dist/scheduler.js',
            watch: false,
            env: { NODE_ENV: 'production' }
        },
        {
            name: 'inventory-job-creator',
            script: './dist/jobCreator.js',
            watch: false,
            env: { NODE_ENV: 'production' }
        },
        {
            name: 'inventory-worker',
            script: './dist/worker.js',
            instances: 2, // *** Run 3 instances of the inventory worker ***
            exec_mode: 'cluster',
            watch: false,
            max_memory_restart: '500M', // Restart if memory exceeds limit (adjust based on need)
            env: { NODE_ENV: 'production' }
        },
        {
            name: 'bull-dashboard',
            script: './dist/dashboard.js',
            watch: false,
            env: {
                NODE_ENV: 'production'
            }
        }
    ]
};
