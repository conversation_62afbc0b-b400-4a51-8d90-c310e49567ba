{"rules": {"class-name": true, "comment-format": [true, "check-space"], "no-duplicate-variable": true, "no-eval": true, "no-internal-module": true, "no-trailing-whitespace": false, "no-var-keyword": true, "no-submodule-imports": false, "quotemark": [false, "single"], "semicolon": [false, "always"], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [false, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}, {"call-signature": "onespace", "index-signature": "onespace", "parameter": "onespace", "property-declaration": "onespace", "variable-declaration": "onespace"}], "variable-name": [true, "ban-keywords"], "whitespace": [false, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"], "jsdoc-format": true, "no-consecutive-blank-lines": false, "one-variable-per-declaration": [true, "ignore-for-loop"], "curly": true, "no-empty": true, "no-unused-expression": true, "eofline": false, "trailing-comma": [false, {"singleline": "never", "multiline": "never"}]}, "extends": ["tslint:latest", "tslint-config-prettier"]}